// 导入应用配置
import appConfig from '../config/appConfig.js';
import { request } from '../services/apiService.js';

/**
 * 会员订阅相关API
 */
const subscriptionApi = {
  /**
   * 获取所有订阅计划
   * @returns {Promise} 请求结果
   */
  getSubscriptionPlans: async () => {
    try {
      return await request('/subscription/plans', 'GET');
    } catch (error) {
      console.error('获取订阅计划失败:', error);
      return { success: false, error: error.message || '获取订阅计划失败' };
    }
  },

  /**
   * 获取当前用户的订阅信息
   * @returns {Promise} 请求结果
   */
  getSubscriptionInfo: async () => {
    try {
      return await request('/subscription/info', 'GET');
    } catch (error) {
      console.error('获取订阅信息失败:', error);
      return { success: false, error: error.message || '获取订阅信息失败' };
    }
  },

  /**
   * 创建订阅
   * @param {Object} data - 订阅数据
   * @returns {Promise} 请求结果
   */
  createSubscription: async (data) => {
    try {
      return await request('/subscription/create', 'POST', data);
    } catch (error) {
      console.error('创建订阅失败:', error);
      return { success: false, error: error.message || '创建订阅失败' };
    }
  },

  /**
   * 更新自动续费状态
   * @param {string} subscriptionId - 订阅ID
   * @param {boolean} autoRenew - 自动续费状态
   * @returns {Promise} 请求结果
   */
  updateAutoRenew: async (subscriptionId, autoRenew) => {
    try {
      return await request(`/subscription/${subscriptionId}/auto-renew`, 'POST', { auto_renew: autoRenew });
    } catch (error) {
      console.error('更新自动续费状态失败:', error);
      return { success: false, error: error.message || '更新自动续费状态失败' };
    }
  },

  /**
   * 获取订阅历史
   * @returns {Promise} 请求结果
   */
  getSubscriptionHistory: async () => {
    try {
      return await request('/subscription/history', 'GET');
    } catch (error) {
      console.error('获取订阅历史失败:', error);
      return { success: false, error: error.message || '获取订阅历史失败' };
    }
  },

  /**
   * 获取支付结果
   * @param {string} paymentId - 支付ID
   * @returns {Promise} 请求结果
   */
  getPaymentResult: async (paymentId) => {
    try {
      return await request(`/subscription/payment/${paymentId}`, 'GET');
    } catch (error) {
      console.error('获取支付结果失败:', error);
      return { success: false, error: error.message || '获取支付结果失败' };
    }
  }
};

/**
 * 管理员会员订阅相关API
 */
const adminSubscriptionApi = {
  /**
   * 获取所有订阅计划
   * @param {boolean} activeOnly - 是否只获取激活的计划
   * @returns {Promise} 请求结果
   */
  getAllSubscriptionPlans: async (activeOnly = true) => {
    try {
      return await request('/admin/subscription/plans', 'GET', null, {
        params: { active_only: activeOnly }
      });
    } catch (error) {
      console.error('获取所有订阅计划失败:', error);
      return { success: false, error: error.message || '获取所有订阅计划失败' };
    }
  },

  /**
   * 创建订阅计划
   * @param {Object} planData - 计划数据
   * @returns {Promise} 请求结果
   */
  createSubscriptionPlan: async (planData) => {
    try {
      return await request('/admin/subscription/plans', 'POST', planData);
    } catch (error) {
      console.error('创建订阅计划失败:', error);
      return { success: false, error: error.message || '创建订阅计划失败' };
    }
  },

  /**
   * 更新订阅计划
   * @param {string} planId - 计划ID
   * @param {Object} planData - 计划数据
   * @returns {Promise} 请求结果
   */
  updateSubscriptionPlan: async (planId, planData) => {
    try {
      return await request(`/admin/subscription/plans/${planId}`, 'PUT', planData);
    } catch (error) {
      console.error('更新订阅计划失败:', error);
      return { success: false, error: error.message || '更新订阅计划失败' };
    }
  },

  /**
   * 删除订阅计划
   * @param {string} planId - 计划ID
   * @returns {Promise} 请求结果
   */
  deleteSubscriptionPlan: async (planId) => {
    try {
      return await request(`/admin/subscription/plans/${planId}`, 'DELETE');
    } catch (error) {
      console.error('删除订阅计划失败:', error);
      return { success: false, error: error.message || '删除订阅计划失败' };
    }
  },

  /**
   * 获取所有订阅信息（分页）
   * @param {Object} params - 查询参数
   * @returns {Promise} 请求结果
   */
  getAllSubscriptions: async (params = {}) => {
    try {
      return await request('/admin/subscription/subscriptions', 'GET', null, { params });
    } catch (error) {
      console.error('获取所有订阅信息失败:', error);
      return { success: false, error: error.message || '获取所有订阅信息失败' };
    }
  },

  /**
   * 获取订阅详情
   * @param {string} subscriptionId - 订阅ID
   * @returns {Promise} 请求结果
   */
  getSubscriptionDetail: async (subscriptionId) => {
    try {
      return await request(`/admin/subscription/subscription/${subscriptionId}`, 'GET');
    } catch (error) {
      console.error('获取订阅详情失败:', error);
      return { success: false, error: error.message || '获取订阅详情失败' };
    }
  },

  /**
   * 获取所有支付记录（分页）
   * @param {Object} params - 查询参数
   * @returns {Promise} 请求结果
   */
  getAllPayments: async (params = {}) => {
    try {
      return await request('/admin/subscription/payments', 'GET', null, { params });
    } catch (error) {
      console.error('获取所有支付记录失败:', error);
      return { success: false, error: error.message || '获取所有支付记录失败' };
    }
  },

  /**
   * 获取支付详情
   * @param {string} paymentId - 支付ID
   * @returns {Promise} 请求结果
   */
  getPaymentDetail: async (paymentId) => {
    try {
      return await request(`/admin/subscription/payment/${paymentId}`, 'GET');
    } catch (error) {
      console.error('获取支付详情失败:', error);
      return { success: false, error: error.message || '获取支付详情失败' };
    }
  },

  /**
   * 获取订阅统计数据
   * @returns {Promise} 请求结果
   */
  getSubscriptionStats: async () => {
    try {
      return await request('/admin/subscription/stats', 'GET');
    } catch (error) {
      console.error('获取订阅统计数据失败:', error);
      return { success: false, error: error.message || '获取订阅统计数据失败' };
    }
  },

  /**
   * 删除订阅记录
   * @param {string} subscriptionId - 订阅ID
   * @returns {Promise} 请求结果
   */
  deleteSubscription: async (subscriptionId) => {
    try {
      return await request(`/admin/subscription/subscription/${subscriptionId}`, 'DELETE');
    } catch (error) {
      console.error('删除订阅记录失败:', error);
      return { success: false, error: error.message || '删除订阅记录失败' };
    }
  },

  /**
   * 删除支付记录
   * @param {string} paymentId - 支付ID
   * @returns {Promise} 请求结果
   */
  deletePayment: async (paymentId) => {
    try {
      return await request(`/admin/subscription/payment/${paymentId}`, 'DELETE');
    } catch (error) {
      console.error('删除支付记录失败:', error);
      return { success: false, error: error.message || '删除支付记录失败' };
    }
  }
};

export { subscriptionApi, adminSubscriptionApi };
