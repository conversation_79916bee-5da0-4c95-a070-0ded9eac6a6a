<template>
  <view class="pull-refresh-container" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd">
    <view class="pull-refresh-indicator" :style="{ height: `${pullDistance}px`, opacity: pullDistance > 0 ? 1 : 0 }">
      <view class="pull-refresh-spinner-container">
        <view class="pull-refresh-spinner" :class="{ refreshing: isRefreshing }" :style="spinnerStyle"></view>
      </view>
      <text class="pull-refresh-text">{{ refreshText }}</text>
    </view>

    <view class="pull-refresh-content" :style="{ transform: `translateY(${pullDistance}px)` }">
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PullToRefresh',
  props: {
    onRefresh: {
      type: Function,
      required: true
    },
    pullDownThreshold: {
      type: Number,
      default: 80
    },
    maxPullDownDistance: {
      type: Number,
      default: 120
    },
    backgroundColor: {
      type: String,
      default: '#f8f8f8'
    }
  },
  data() {
    return {
      isPulling: false,
      pullDistance: 0,
      isRefreshing: false,
      startY: null,
      currentY: null
    }
  },
  computed: {
    refreshText() {
      if (this.isRefreshing) {
        return '正在刷新...'
      } else if (this.pullDistance >= this.pullDownThreshold) {
        return '释放刷新'
      } else {
        return '下拉刷新'
      }
    },
    spinnerStyle() {
      if (this.isRefreshing) {
        return {}
      } else {
        const rotation = Math.min(this.pullDistance / this.pullDownThreshold * 360, 360)
        return {
          transform: `rotate(${rotation}deg)`
        }
      }
    }
  },
  methods: {
    handleTouchStart(e) {
      // 只在容器滚动到顶部时允许下拉刷新
      if (this.getScrollTop() === 0) {
        this.startY = e.touches[0].clientY
        this.currentY = e.touches[0].clientY
        this.isPulling = true
      }
    },

    handleTouchMove(e) {
      if (!this.isPulling || this.startY === null) return

      this.currentY = e.touches[0].clientY
      const deltaY = this.currentY - this.startY

      // 只有下拉时才响应
      if (deltaY > 0) {
        // 使用平方根函数让刷新指示器的移动距离减慢下来，提供更好的手感
        const newPullDistance = Math.min(
          Math.sqrt(deltaY) * 5,
          this.maxPullDownDistance
        )

        this.pullDistance = newPullDistance

        // 防止页面滚动
        e.preventDefault && e.preventDefault()
      } else {
        // 如果上滑，取消下拉状态
        this.pullDistance = 0
        this.isPulling = false
      }
    },

    async handleTouchEnd() {
      if (!this.isPulling) return

      if (this.pullDistance >= this.pullDownThreshold) {
        // 开始刷新
        this.isRefreshing = true
        this.pullDistance = this.pullDownThreshold / 1.5 // 保持一个较小的距离显示刷新动画

        try {
          await this.onRefresh()
        } catch (error) {
          console.error('刷新失败:', error)
        } finally {
          // 刷新完成，重置状态
          setTimeout(() => {
            this.isRefreshing = false
            this.pullDistance = 0
            this.isPulling = false
          }, 300)
        }
      } else {
        // 未达到刷新阈值，重置状态
        this.pullDistance = 0
        this.isPulling = false
      }

      this.startY = null
      this.currentY = null
    },

    getScrollTop() {
      // 获取页面滚动位置
      try {
        // 在小程序/App环境中，使用uni.pageScrollTo的方式获取滚动位置
        if (typeof uni !== 'undefined') {
          // 由于uni没有直接获取滚动位置的API，我们假设初始位置为0
          // 这是一个简化的处理方式，实际上我们只需要知道是否在顶部
          return 0;
        }

        // 在浏览器环境中，使用标准DOM API
        if (typeof window !== 'undefined') {
          return window.pageYOffset ||
                 (document && document.documentElement && document.documentElement.scrollTop) ||
                 (document && document.body && document.body.scrollTop) ||
                 0;
        }

        // 默认返回0
        return 0;
      } catch (error) {
        console.error('获取滚动位置失败:', error);
        return 0;
      }
    }
  }
}
</script>

<style>
.pull-refresh-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.pull-refresh-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 0;
  overflow: hidden;
  background-color: var(--bg-secondary);
  transition: height 0.2s ease;
}

.pull-refresh-spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
}

.pull-refresh-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-light);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  transition: transform 0.2s ease;
}

.pull-refresh-spinner.refreshing {
  animation: spin 1s linear infinite;
}

.pull-refresh-text {
  font-size: 14px;
  color: var(--text-secondary);
}

.pull-refresh-content {
  width: 100%;
  transition: transform 0.2s ease;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
