<template>
  <view>
    <uni-popup ref="popup" type="center" :safe-area="false">
      <view class="share-popup">
        <view class="share-header">
          <text class="share-title">分享有礼</text>
          <view class="close-btn" @tap="close">
            <uni-icons type="closeempty" size="24" color="#999"></uni-icons>
          </view>
        </view>
        
        <view class="share-content">
          <text class="share-desc">分享给好友，双方都可获得奖励</text>
          
          <view class="share-methods">
            <button class="share-btn" @tap="shareToFriend">
              <image src="/static/icons/weixin.svg" style="width:48px;height:48px"/>
              <text>微信好友</text>
            </button>
            
            <button class="share-btn" @tap="shareToMoment">
              <image src="/static/icons/pengyouquan.svg" style="width:48px;height:48px"/>
              <text>朋友圈</text>
            </button>
            
            <button class="share-btn" @tap="copyLink">
              <image src="/static/icons/paperclip.svg" style="width:48px;height:48px"/>
              <text>复制链接</text>
            </button>
          </view>
        </view>
        
        <view class="view-records-btn" @click="viewRecords">
          <svg-icon type="list" size="20" color="#6b9ac4"></svg-icon>
          <text>查看分享记录</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  name: 'SharePopup',
  methods: {
    open() {
      this.$refs.popup.open()
    },
    close() {
      this.$refs.popup.close()
    },
    async shareToMoment() {
      try {
        // 获取用户信息
        const user = this.$store.state?.user?.user || {};
        const userId = user.user_id || user.id || user._id || user.userId;
        
        // 获取分享配置
        const shareConfig = await this.getShareConfig();
        const shareLink = await this.generateShareLink(userId);
        
        uni.share({
          provider: 'weixin',
          scene: 'WXSenceTimeline',
          type: 0,
          title: shareConfig.momentTitle.replace('{name}', user.name || '好友'),
          summary: shareConfig.momentDesc,
          imageUrl: shareConfig.imageUrl,
          href: shareLink,
          success: () => {
            uni.showToast({ title: '分享成功' });
            this.logShareAction(userId, 'share_moment');
          },
          fail: (err) => {
            console.error('朋友圈分享失败:', err);
            uni.showToast({ title: '分享失败', icon: 'none' });
          }
        });
      } catch (error) {
        console.error('分享准备失败:', error);
        uni.showToast({ title: '分享功能暂不可用', icon: 'none' });
      }
    },
    async copyLink() {
      try {
        // 深度检查store状态结构
        console.log('Store状态深度检查:', {
          fullState: JSON.parse(JSON.stringify(this.$store.state)),
          getters: {
            isLoggedIn: this.$store.getters.isLoggedIn
          },
          localStorage: uni.getStorageSync('user_info')
        });

        // 获取用户信息的更健壮方式
        const user = this.$store.state?.user?.user || 
                    this.$store.state?.user || 
                    uni.getStorageSync('user_info') || 
                    {};
        
        const userId = user.user_id || user.id || user._id || user.userId;

        // 检查登录状态的更可靠方式
        const isActuallyLoggedIn = this.$store.getters.isLoggedIn || 
                                  (userId && this.$store.state?.user?.token);

        if (!isActuallyLoggedIn) {
          console.error('用户登录状态异常 - 完整调试信息:', {
            storeState: this.$store.state,
            getters: this.$store.getters,
            localStorage: uni.getStorageSync('user_info'),
            currentUser: user
          });
          uni.showToast({
            title: '请重新登录',
            icon: 'none',
            duration: 2000
          });
          this.close();
          // 强制清除可能无效的状态
          this.$store.dispatch('user/logout');
          return;
        }

        // 检查用户ID（与profile页面完全一致）
        if (!userId) {
          console.warn('用户ID缺失 - 详细状态:', {
            storeState: this.$store.state,
            currentUser: user
          });
          uni.showToast({
            title: '用户信息异常，请重新登录',
            icon: 'none',
            duration: 2000
          });
          // 尝试刷新用户状态（与profile页面一致）
          this.$store.dispatch('user/refreshUserInfo');
          this.close();
          return;
        }

        try {
          // 多层级config回退方案
          let baseUrl = '';
          
          // 1. 尝试从全局$config获取
          if (this.$config?.baseUrl) {
            baseUrl = this.$config.baseUrl;
          } 
          // 2. 尝试从appConfig.js获取
          else if (typeof getAppConfig !== 'undefined') {
            const appConfig = getAppConfig();
            baseUrl = appConfig?.baseUrl;
          }
          // 3. 尝试从环境变量获取
          else if (process.env.VUE_APP_BASE_URL) {
            baseUrl = process.env.VUE_APP_BASE_URL;
          }
          // 4. 使用默认值
          else {
            baseUrl = 'https://soulsync.example.com';
            console.warn('使用默认baseUrl，建议配置正确的baseUrl');
          }
          
          // 确保URL格式正确
          baseUrl = baseUrl.replace(/\/+$/, '');
          const shareLink = `${baseUrl}/share/invite?userId=${encodeURIComponent(userId)}&t=${Date.now()}`;
          
          console.log('生成的分享链接:', shareLink);
          
          // 添加复制操作的状态反馈
          uni.setClipboardData({
            data: shareLink,
            success: () => {
              console.log('复制成功');
              uni.showToast({
                title: '链接已复制',
                icon: 'success',
                duration: 2000
              });
              // 自动关闭弹窗
              setTimeout(() => this.close(), 1500);
            },
            fail: (err) => {
              console.error('复制失败:', err);
              uni.showToast({
                title: '复制失败，请重试',
                icon: 'none',
                duration: 2000
              });
            }
          });
          
          return shareLink;
        } catch (error) {
          console.error('生成分享链接失败:', error);
          uni.showToast({
            title: '分享功能暂时不可用',
            icon: 'none',
            duration: 2000
          });
          throw error;
        }
        
        uni.setClipboardData({
          data: shareLink,
          success: () => {
            uni.showToast({ title: '链接已复制' });
            this.logShareAction(userId, 'copy_link');
          },
          fail: () => {
            uni.showToast({ title: '复制失败', icon: 'none' });
          }
        });
      } catch (error) {
        console.error('复制链接失败:', error);
        uni.showToast({ title: '操作失败', icon: 'none' });
      }
    },
    
    logShareAction(userId, actionType) {
      this.$api.post('/track/share', {
        userId,
        actionType,
        timestamp: new Date().toISOString()
      }).catch(e => console.error('记录分享失败:', e));
    },
    async shareToFriend() {
      try {
        // 获取用户信息
        const user = this.$store.state?.user?.user || {};
        const userId = user.user_id || user.id || user._id || user.userId;
        
        // 获取分享配置
        const shareConfig = await this.getShareConfig();
        const shareLink = await this.generateShareLink(userId);
        
        // APP端分享
        if (uni.getSystemInfoSync().uniPlatform === 'app') {
          plus.share.sendWithSystem({
            type: 'text',
            content: shareConfig.friendContent.replace('{name}', user.name || '你'),
            href: shareLink,
            title: shareConfig.friendTitle,
            thumbs: [shareConfig.imageUrl],
            success: () => {
              uni.showToast({ title: '分享成功' });
              this.close();
              this.logShareAction(userId, 'share_friend');
            },
            fail: (err) => {
              console.error('微信好友分享失败:', err);
              uni.showToast({ title: '分享失败', icon: 'none' });
            }
          });
        } 
        // 小程序端分享
        else {
          uni.share({
            provider: 'weixin',
            scene: 'WXSceneSession',
            type: 0,
            title: shareConfig.friendTitle,
            summary: shareConfig.friendDesc,
            imageUrl: shareConfig.imageUrl,
            href: shareLink,
            success: () => {
              uni.showToast({ title: '分享成功' });
              this.close();
              this.logShareAction(userId, 'share_friend');
            },
            fail: (err) => {
              console.error('微信好友分享失败:', err);
              uni.showToast({ title: '分享失败', icon: 'none' });
            }
          });
        }
      } catch (error) {
        console.error('分享准备失败:', error);
        uni.showToast({ title: '分享功能暂不可用', icon: 'none' });
      }
    },
    
    async generateShareLink(userId) {
      try {
        // 多层级config回退方案
        let baseUrl = '';
        
        // 1. 尝试从全局$config获取
        if (this.$config?.baseUrl) {
          baseUrl = this.$config.baseUrl;
        } 
        // 2. 尝试从appConfig.js获取
        else if (typeof getAppConfig !== 'undefined') {
          const appConfig = getAppConfig();
          baseUrl = appConfig?.baseUrl;
        }
        // 3. 尝试从环境变量获取
        else if (process.env.VUE_APP_BASE_URL) {
          baseUrl = process.env.VUE_APP_BASE_URL;
        }
        // 4. 使用默认值
        else {
          baseUrl = 'https://soulsync.example.com';
          console.warn('使用默认baseUrl，建议配置正确的baseUrl');
        }
        
        // 确保URL格式正确
        baseUrl = baseUrl.replace(/\/+$/, '');
        return `${baseUrl}/share/invite?inviter=${encodeURIComponent(userId)}&t=${Date.now()}`;
      } catch (error) {
        console.error('生成分享链接失败:', error);
        throw error;
      }
    },

    viewRecords() {
      this.close();
      uni.navigateTo({
        url: '/pages/profile/share-records'
      });
    },
    
    async getShareConfig() {
      try {
        // 使用uni.request替代$api
        const response = await new Promise((resolve, reject) => {
          uni.request({
            url: '/api/share/config',
            method: 'GET',
            success: (res) => resolve(res.data),
            fail: reject
          });
        });
        
        if (response?.success) {
          return response.data;
        }
        throw new Error('接口返回数据格式不正确');
      } catch (error) {
        console.error('获取分享配置失败，使用默认配置:', error);
        return {
          friendTitle: '分享有礼 - SoulSync',
          friendDesc: '快来加入SoulSync，分享即可获得奖励',
          friendContent: '{name}邀请你加入SoulSync，立即注册领取奖励',
          momentTitle: '{name}邀请你加入SoulSync',
          momentDesc: '专业的心理健康服务平台',
          imageUrl: 'https://soulsync.example.com/share.jpg'
        };
      }
    },
  }
}
</script>

<style scoped>

.share-popup {
  background-color: #fff;
  border-radius: 16px;
  padding: 24px;
  width: 80%;
  max-width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.share-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.close-btn {
  width: 24px;
  height: 24px;
}

.close-btn image {
  width: 100%;
  height: 100%;
}

.share-desc {
  font-size: 14px;
  color: #888;
  text-align: center;
  margin-bottom: 24px;
  display: block;
}

.share-methods {
  display: flex;
  justify-content: space-around;
  padding: 0 10px;
  margin-top: 16px;
}

.share-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: none;
  padding: 8px 12px;
  margin: 0 4px;
}

.share-btn image {
  width: 48px;
  height: 48px;
  margin-bottom: 8px;
}

.share-btn text {
  font-size: 12px;
  color: #666;
}
.view-records-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  margin-top: 20rpx;
  color: #6b9ac4;
  font-size: 28rpx;
  border-top: 1rpx solid #f0f0f0;
}

.view-records-btn text {
  margin-left: 10rpx;
}

</style>