<template>
  <view class="uni-popup" v-if="isOpen" @click="handleMaskClick">
    <view class="uni-popup__mask"></view>
    <view 
      class="uni-popup__wrapper" 
      :class="[`uni-popup__wrapper-${type}`]"
      @click.stop
    >
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UniPopup',
  props: {
    // 弹出层类型，可选值：top/bottom/center
    type: {
      type: String,
      default: 'center'
    },
    // 是否显示遮罩
    mask: {
      type: Boolean,
      default: true
    },
    // 点击遮罩是否关闭弹窗
    maskClick: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isOpen: false
    }
  },
  methods: {
    open() {
      this.isOpen = true
    },
    close() {
      this.isOpen = false
    },
    handleMaskClick() {
      if (this.maskClick) {
        this.close()
      }
    }
  }
}
</script>

<style>
.uni-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.uni-popup__mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
}

.uni-popup__wrapper {
  position: absolute;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.uni-popup__wrapper-center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 80%;
  max-height: 80%;
}

.uni-popup__wrapper-top {
  top: 0;
  left: 0;
  right: 0;
  border-radius: 0 0 8px 8px;
}

.uni-popup__wrapper-bottom {
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 8px 8px 0 0;
}
</style>
