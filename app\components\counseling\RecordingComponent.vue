<template>
  <view class="recording-container">
    <view class="recording-header">
      <text class="recording-title">咨询内容录音</text>
    </view>

    <view v-if="!audioUri" class="recording-content">
      <view class="alert-box info-alert" v-if="!isRecording">
        <text class="alert-icon">ℹ️</text>
        <view class="alert-content">
          <text class="alert-title">准备录制咨询内容</text>
          <text class="alert-description">点击开始倾听按钮，系统将录制咨询内容并自动识别咨询师和来访者角色。录制完成后将自动处理并生成分析报告。</text>
        </view>
      </view>

      <view class="alert-box warning-alert" v-if="isRecording">
        <text class="alert-icon">⚠️</text>
        <view class="alert-content">
          <text class="alert-title">正在录制...</text>
          <text class="alert-description">系统正在录制咨询对话，请确保咨询师和客户声音清晰。</text>
        </view>
      </view>

      <view class="recording-controls">
        <view v-if="isRecording" class="recording-time">
          <text class="time-text">{{ formatTime(recordingTime) }}</text>
        </view>

        <view class="audio-level-container" v-if="isRecording">
          <view class="audio-level-bar">
            <view class="audio-level-fill" :style="{ width: audioLevel + '%' }"></view>
          </view>
        </view>

        <view class="button-container">
          <button
            v-if="!isRecording"
            class="record-button start-button"
            @click="startRecording"
          >
            <text class="button-icon">👂</text>
            <text class="button-text">开始倾听</text>
          </button>

          <button
            v-else
            class="record-button stop-button"
            @click="stopRecording"
          >
            <text class="button-icon">⏹️</text>
            <text class="button-text">停止录制</text>
          </button>
        </view>
      </view>
    </view>

    <view v-else class="recording-content">
      <view v-if="!uploadSuccess" class="upload-section">
        <view class="alert-box success-alert">
          <text class="alert-icon">✅</text>
          <view class="alert-content">
            <text class="alert-title">录制完成</text>
            <text class="alert-description">咨询对话录制已完成，点击上传按钮将录音发送到服务器进行处理。</text>
          </view>
        </view>

        <view class="recording-info">
          <text class="recording-info-item">录音时长: {{ formatTime(recordingTime) }}</text>
          <text class="recording-info-item">文件大小: {{ getFileSizeText() }}</text>
        </view>

        <view class="audio-player">
          <button class="play-button" @click="playRecording">
            <text class="button-icon">▶️</text>
            <text class="button-text">播放录音</text>
          </button>
        </view>

        <view class="button-group">
          <button
            class="secondary-button"
            @click="resetRecording"
          >
            <text class="button-icon">🔄</text>
            <text class="button-text">重新录制</text>
          </button>

          <button
            class="primary-button upload-button"
            :disabled="isUploading"
            @click="uploadRecording"
          >
            <text class="button-icon">📤</text>
            <text class="button-text">{{ isUploading ? '上传中...' : '上传录音' }}</text>
          </button>
        </view>
      </view>

      <view v-else-if="!processingComplete" class="process-section">
        <view class="alert-box success-alert">
          <text class="alert-icon">✅</text>
          <view class="alert-content">
            <text class="alert-title">上传成功</text>
            <text class="alert-description">录音上传成功，点击处理按钮开始转录和说话人识别。</text>
          </view>
        </view>

        <view class="recording-info">
          <text class="recording-info-item">录音ID: {{ recordingId }}</text>
          <text class="recording-info-item">录音时长: {{ formatTime(recordingTime) }}</text>
        </view>

        <view class="audio-player">
          <button class="play-button" @click="playRecording">
            <text class="button-icon">▶️</text>
            <text class="button-text">播放录音</text>
          </button>
        </view>

        <view class="process-info">
          <text class="process-info-text">点击处理按钮后，系统将执行以下步骤：</text>
          <view class="process-step">
            <text class="step-number">1</text>
            <text class="step-text">转录录音内容</text>
          </view>
          <view class="process-step">
            <text class="step-number">2</text>
            <text class="step-text">识别说话人角色</text>
          </view>
          <view class="process-step">
            <text class="step-number">3</text>
            <text class="step-text">准备分析数据</text>
          </view>
        </view>

        <button
          class="primary-button process-button"
          :disabled="isProcessing"
          @click="processRecording"
        >
          <text class="button-icon">🔄</text>
          <text class="button-text">{{ isProcessing ? '处理中...' : '开始处理' }}</text>
        </button>
      </view>

      <view v-else class="complete-section">
        <view class="alert-box success-alert">
          <text class="alert-icon">🎉</text>
          <view class="alert-content">
            <text class="alert-title">处理完成</text>
            <text class="alert-description">录音处理完成，点击分析按钮开始分析对话内容。</text>
          </view>
        </view>

        <view class="recording-info">
          <text class="recording-info-item">录音ID: {{ recordingId }}</text>
          <text class="recording-info-item">录音时长: {{ formatTime(recordingTime) }}</text>
          <text class="recording-info-item">处理状态: 已完成</text>
        </view>

        <view class="audio-player">
          <button class="play-button" @click="playRecording">
            <text class="button-icon">▶️</text>
            <text class="button-text">播放录音</text>
          </button>
        </view>

        <view class="analysis-info">
          <text class="analysis-info-text">点击分析按钮后，系统将执行以下步骤：</text>
          <view class="analysis-step">
            <text class="step-number">1</text>
            <text class="step-text">分析对话内容</text>
          </view>
          <view class="analysis-step">
            <text class="step-number">2</text>
            <text class="step-text">评估心理状态</text>
          </view>
          <view class="analysis-step">
            <text class="step-number">3</text>
            <text class="step-text">生成分析报告</text>
          </view>
        </view>

        <view class="disclaimer">
          <text class="disclaimer-text">注意：分析过程可能需要几分钟时间，请耐心等待。分析完成后将自动跳转到报告页面。</text>
        </view>

        <button
          class="primary-button analyze-button"
          :disabled="isAnalyzing"
          @click="startAnalysis"
        >
          <text class="button-icon">📊</text>
          <text class="button-text">{{ isAnalyzing ? '分析中...' : '开始分析' }}</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
// 使用uni-app的录音API
import counselingService from '../../services/counselingService';
import { APP_SETTINGS } from '../../config/appConfig';

export default {
  props: {
    sessionId: {
      type: [String, Number],
      required: true
    },
    onRecordingComplete: {
      type: Function,
      default: null
    },
    onTranscriptionComplete: {
      type: Function,
      default: null
    },
    onAnalysisComplete: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      isRecording: false,
      recordingTime: 0,
      audioUri: null,
      audioLevel: 0,
      isUploading: false,
      uploadSuccess: false,
      recordingId: null,
      isProcessing: false,
      processingComplete: false,
      isAnalyzing: false,
      transcriptionPollingTimer: null,
      // Web Audio API 相关变量
      mediaRecorder: null,
      audioChunks: [],
      audioBlob: null,
      isWebAudio: false,
      // 文件大小
      audioSize: 0,
      // 处理进度
      processingProgress: 0,
      // 分析进度
      analysisProgress: 0,
      // 确认对话框
      showConfirmReset: false
    }
  },
  beforeDestroy() {
    this.cleanupResources();
  },
  methods: {
    // 清理资源
    cleanupResources() {
      // 清除计时器
      if (this.timerRef) {
        clearInterval(this.timerRef);
        this.timerRef = null;
      }

      if (this.audioLevelTimerRef) {
        clearInterval(this.audioLevelTimerRef);
        this.audioLevelTimerRef = null;
      }

      if (this.transcriptionPollingTimer) {
        clearInterval(this.transcriptionPollingTimer);
        this.transcriptionPollingTimer = null;
      }

      // 停止录音
      this.stopRecordingInternal();

      // 清理Web Audio相关资源
      if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
        try {
          this.mediaRecorder.stop();
        } catch (error) {
          console.error('停止Web Audio录音失败:', error);
        }
      }

      this.mediaRecorder = null;
      this.audioChunks = [];
    },

    // 格式化时间
    formatTime(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // 开始录音
    async startRecording() {
      try {
        // 检测是否在浏览器环境
        let isWeb = false;
        try {
          // 尝试访问 navigator.mediaDevices，这只在浏览器环境中可用
          if (typeof navigator !== 'undefined' && navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            isWeb = true;
          }
        } catch (e) {
          console.log('非浏览器环境');
        }

        this.isWebAudio = isWeb;

        if (isWeb) {
          // 在浏览器环境中，使用Web Audio API
          console.log('使用Web Audio API录音');
          await this.startWebAudioRecording();
        } else {
          // 在非浏览器环境中，使用uni-app API
          console.log('使用uni-app API录音');
          await this.startUniAppRecording();
        }
      } catch (error) {
        console.error('开始录音失败:', error);
        this.isRecording = false;

        // 根据平台提供不同的错误提示
        let errorMessage = '无法开始录音: ' + (error.message || error.errMsg || '未知错误');
        let helpMessage = '';

        if (uni.getSystemInfoSync().platform === 'web') {
          helpMessage = '\n\n请确保您已在浏览器中允许麦克风访问权限。';
        } else {
          helpMessage = '\n\n请确保已给予应用麦克风权限。';
        }

        uni.showModal({
          title: '录音错误',
          content: errorMessage + helpMessage,
          showCancel: false
        });
      }
    },

    // 使用Web Audio API录音（浏览器环境）
    async startWebAudioRecording() {
      try {
        // 检查是否在浏览器环境
        if (typeof navigator === 'undefined' || !navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          console.error('当前环境不支持Web Audio API');
          throw new Error('当前环境不支持Web Audio API');
        }

        console.log('请求麦克风权限...');
        // 请求麦克风权限
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        console.log('麦克风权限获取成功');

        // 创建 MediaRecorder
        this.audioChunks = [];
        this.mediaRecorder = new MediaRecorder(stream);

        // 监听数据可用事件
        this.mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            this.audioChunks.push(event.data);
          }
        };

        // 监听录音停止事件
        this.mediaRecorder.onstop = () => {
          // 创建 Blob 对象
          this.audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });

          // 创建 URL
          this.audioUri = URL.createObjectURL(this.audioBlob);
          this.isRecording = false;

          // 清除计时器
          if (this.timerRef) {
            clearInterval(this.timerRef);
            this.timerRef = null;
          }

          if (this.audioLevelTimerRef) {
            clearInterval(this.audioLevelTimerRef);
            this.audioLevelTimerRef = null;
          }

          console.log('录音已停止，生成URL:', this.audioUri);

          // 自动上传和处理录音
          this.autoProcessRecording();
        };

        // 监听错误事件
        this.mediaRecorder.onerror = (error) => {
          console.error('Web Audio 录音错误:', error);
          this.isRecording = false;

          if (this.timerRef) {
            clearInterval(this.timerRef);
            this.timerRef = null;
          }

          if (this.audioLevelTimerRef) {
            clearInterval(this.audioLevelTimerRef);
            this.audioLevelTimerRef = null;
          }

          uni.showModal({
            title: '录音错误',
            content: '录音过程中发生错误',
            showCancel: false
          });
        };

        // 请求每500毫秒收集一次数据
        this.mediaRecorder.start(500);
        console.log('Web Audio 录音已开始');
        this.isRecording = true;

        // 开始计时
        this.timerRef = setInterval(() => {
          this.recordingTime += 1;

          // 设置最大录音时长
          if (this.recordingTime >= APP_SETTINGS.recording.maxDuration) {
            this.stopRecording();
          }
        }, 1000);

        // 开始监测音量
        this.startAudioLevelMonitoring();
      } catch (error) {
        console.error('Web Audio 录音初始化失败:', error);
        throw error; // 将错误传递给上层函数处理
      }
    },

    // 使用uni-app API录音（非浏览器环境）
    async startUniAppRecording() {
      // 检查是否在浏览器环境
      if (typeof navigator !== 'undefined' && navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        console.error('在浏览器环境中调用了uni-app API');
        throw new Error('浏览器环境不支持uni-app录音API');
      }

      try {
        // 尝试使用uni.authorize
        try {
          const res = await uni.authorize({
            scope: 'scope.record'
          }).catch(err => {
            console.error('请求录音权限失败:', err);
            return { errMsg: err.errMsg || '权限请求失败' };
          });

          if (res.errMsg && res.errMsg.indexOf('ok') === -1) {
            uni.showModal({
              title: '权限错误',
              content: '需要麦克风权限才能录音',
              showCancel: false
            });
            return;
          }
        } catch (err) {
          console.error('请求录音权限失败:', err);
          // 如果权限请求失败，继续尝试录音，因为某些平台可能会在录音时自动请求权限
        }

        // 创建录音管理器
        this.recorderManager = uni.getRecorderManager();

        // 监听录音事件
        this.recorderManager.onStart(() => {
          console.log('录音已开始');
          this.isRecording = true;

          // 开始计时
          this.timerRef = setInterval(() => {
            this.recordingTime += 1;
          }, 1000);

          // 开始监测音量
          this.startAudioLevelMonitoring();
        });

        this.recorderManager.onError((res) => {
          console.error('录音错误:', res);
          uni.showModal({
            title: '录音错误',
            content: res.errMsg || '录音过程中发生错误',
            showCancel: false
          });
        });

        this.recorderManager.onStop((res) => {
          console.log('录音已停止', res);
          this.audioUri = res.tempFilePath;
          this.isRecording = false;

          // 清除计时器
          if (this.timerRef) {
            clearInterval(this.timerRef);
            this.timerRef = null;
          }

          if (this.audioLevelTimerRef) {
            clearInterval(this.audioLevelTimerRef);
            this.audioLevelTimerRef = null;
          }
        });

        // 开始录音
        const options = {
          duration: APP_SETTINGS.recording.maxDuration * 1000, // 最长录音时长，单位毫秒
          sampleRate: APP_SETTINGS.recording.sampleRate,
          numberOfChannels: APP_SETTINGS.recording.numberOfChannels,
          encodeBitRate: APP_SETTINGS.recording.bitRate,
          format: 'wav',
          frameSize: 50
        };

        this.recorderManager.start(options);
      } catch (error) {
        console.error('使用uni-app API录音失败:', error);
        throw error; // 将错误传递给上层函数处理
      }
    },

    // 开始监测音量
    startAudioLevelMonitoring() {
      // 在React Native中，我们无法直接获取音量级别
      // 这里使用模拟的方式生成音量数据
      this.audioLevelTimerRef = setInterval(() => {
        if (this.isRecording) {
          // 生成随机音量级别，模拟真实录音环境
          const randomLevel = Math.floor(Math.random() * 60) + 10; // 10-70范围内的随机值
          this.audioLevel = randomLevel;
        }
      }, 100);
    },

    // 停止录音
    async stopRecording() {
      try {
        if (!this.isRecording) {
          return;
        }

        console.log('停止录音, isWebAudio:', this.isWebAudio);

        // 显示加载提示
        uni.showLoading({
          title: '处理中...'
        });

        if (this.isWebAudio) {
          // 使用Web Audio API停止录音
          if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
            console.log('停止Web Audio录音');
            // 请求收集数据
            this.mediaRecorder.requestData();
            // 停止录音
            this.mediaRecorder.stop();
            // 注意：录音结果将在onstop事件中处理，然后会自动调用上传和处理
          } else {
            console.warn('录音器不存在或已停止');
            uni.hideLoading();
          }
        } else {
          // 使用uni-app API停止录音
          if (this.recorderManager) {
            console.log('停止uni-app录音');
            this.recorderManager.stop();
            // 注意：录音结果将在onStop事件中处理

            // 在onStop事件中，我们需要等待audioUri被设置
            // 因此，我们在这里设置一个定时器来检查audioUri
            this.checkAudioUriAndProcess();
          } else {
            console.warn('录音管理器不存在');
            uni.hideLoading();
          }
        }
      } catch (error) {
        console.error('停止录音失败:', error);
        this.isRecording = false;
        uni.hideLoading();

        // 清除计时器
        if (this.timerRef) {
          clearInterval(this.timerRef);
          this.timerRef = null;
        }

        if (this.audioLevelTimerRef) {
          clearInterval(this.audioLevelTimerRef);
          this.audioLevelTimerRef = null;
        }

        uni.showModal({
          title: '录音错误',
          content: '停止录音失败: ' + (error.message || error.errMsg || '未知错误'),
          showCancel: false
        });
      }
    },

    // 检查audioUri是否已设置，然后自动上传和处理
    checkAudioUriAndProcess() {
      // 最多等待10秒
      let attempts = 0;
      const maxAttempts = 20; // 每500ms检查一次，最多10秒

      const checkInterval = setInterval(() => {
        attempts++;

        if (this.audioUri) {
          clearInterval(checkInterval);
          // 自动上传和处理
          this.autoProcessRecording();
        } else if (attempts >= maxAttempts) {
          clearInterval(checkInterval);
          uni.hideLoading();
          uni.showModal({
            title: '录音错误',
            content: '无法获取录音文件',
            showCancel: false
          });
        }
      }, 500);
    },

    // 自动上传和处理录音
    async autoProcessRecording() {
      try {
        console.log('开始自动处理录音流程...');

        // 1. 上传录音
        console.log('步骤1: 上传录音');
        await this.uploadRecording();

        // 2. 如果上传成功，处理录音
        if (this.uploadSuccess && this.recordingId) {
          console.log('步骤2: 处理录音 (转录和说话人识别)');
          await this.processRecording();

          // 3. 如果处理成功，开始分析
          if (this.processingComplete) {
            console.log('步骤3: 开始分析');
            // 延迟5秒再开始分析，确保转录数据已经保存到数据库
            console.log('等待5秒，确保转录数据已保存到数据库...');

            // 显示等待提示
            uni.showToast({
              title: '准备分析数据...',
              icon: 'none',
              duration: 3000
            });

            setTimeout(async () => {
              try {
                // 再次检查转录状态
                const transcriptionResult = await counselingService.getTranscription(this.recordingId);
                if (transcriptionResult.success && transcriptionResult.status === 'completed') {
                  console.log('转录已完成，开始分析...');
                  await this.startAnalysis();
                } else {
                  console.warn('转录尚未完成，等待更长时间...');
                  // 如果转录尚未完成，再等待5秒
                  uni.showToast({
                    title: '等待转录完成...',
                    icon: 'none',
                    duration: 3000
                  });

                  setTimeout(async () => {
                    try {
                      await this.startAnalysis();
                    } catch (analysisError) {
                      console.error('二次延迟分析失败:', analysisError);
                      // 如果二次延迟分析失败，再尝试一次
                      setTimeout(() => {
                        this.startAnalysis(2); // 从重试计数2开始
                      }, 5000);
                    }
                  }, 5000);
                }
              } catch (analysisError) {
                console.error('延迟分析失败:', analysisError);
                // 如果延迟分析失败，再尝试一次
                setTimeout(() => {
                  this.startAnalysis(1); // 从重试计数1开始
                }, 5000);
              }
            }, 5000);
          } else {
            console.warn('处理未完成，不会自动开始分析');
            uni.hideLoading();
          }
        } else {
          console.warn('上传失败或没有录音ID，无法继续处理');
          uni.hideLoading();
        }
      } catch (error) {
        console.error('自动处理录音失败:', error);
        uni.hideLoading();
        uni.showModal({
          title: '处理失败',
          content: '自动处理录音失败: ' + (error.message || '未知错误'),
          showCancel: false
        });
      }
    },

    // 内部停止录音方法
    async stopRecordingInternal() {
      if (this.isRecording) {
        try {
          console.log('内部停止录音, isWebAudio:', this.isWebAudio);

          if (this.isWebAudio) {
            // 使用Web Audio API停止录音
            if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
              console.log('内部停止Web Audio录音');
              // 请求收集数据
              this.mediaRecorder.requestData();
              // 停止录音
              this.mediaRecorder.stop();
            } else {
              console.warn('内部停止: 录音器不存在或已停止');
            }
          } else {
            // 使用uni-app API停止录音
            if (this.recorderManager) {
              console.log('内部停止uni-app录音');
              this.recorderManager.stop();
            } else {
              console.warn('内部停止: 录音管理器不存在');
            }
          }
        } catch (error) {
          console.error('停止录音时出错:', error);
        }
      }
    },

    // 上传录音
    async uploadRecording() {
      if (!this.audioUri) return;

      try {
        this.isUploading = true;

        // 检查文件大小
        let fileSize = 0;
        if (this.isWebAudio && this.audioBlob) {
          fileSize = this.audioBlob.size;
          this.audioSize = fileSize;
        } else {
          try {
            // 获取文件信息
            const fileInfo = await new Promise((resolve, reject) => {
              uni.getFileInfo({
                filePath: this.audioUri,
                success: (res) => resolve(res),
                fail: (err) => reject(err)
              });
            });

            fileSize = fileInfo.size;
            this.audioSize = fileSize;
          } catch (error) {
            console.warn('获取文件信息失败:', error);
          }
        }

        // 检查文件大小是否超过限制
        const maxSize = APP_SETTINGS.recording.maxFileSize;
        if (fileSize > maxSize) {
          uni.showModal({
            title: '文件过大',
            content: `录音文件大小(${Math.round(fileSize/1024/1024)}MB)超过限制(${Math.round(maxSize/1024/1024)}MB)，请重新录制较短的对话。`,
            showCancel: false
          });
          this.isUploading = false;
          return;
        }

        uni.showLoading({
          title: '上传中...'
        });

        let result;

        if (this.isWebAudio && this.audioBlob) {
          // 在H5环境中，需要将Blob转换为File并上传
          const file = new File([this.audioBlob], 'recording.wav', { type: 'audio/wav' });

          // 创建FormData
          const formData = new FormData();
          formData.append('file', file);
          formData.append('session_id', this.sessionId);

          // 使用fetch API上传
          const response = await fetch(APP_SETTINGS.apiBaseUrl + '/api/recordings/upload', {
            method: 'POST',
            body: formData
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          result = await response.json();
        } else {
          // 在非H5环境中，使用uni-app API上传
          result = await counselingService.uploadRecording(this.audioUri, this.sessionId);
        }

        uni.hideLoading();

        if (result.success) {
          this.uploadSuccess = true;
          this.recordingId = result.recording_id;

          uni.showToast({
            title: '上传成功',
            icon: 'success'
          });

          if (this.onRecordingComplete) {
            this.onRecordingComplete(result.recording_id);
          }
        } else {
          uni.showModal({
            title: '上传失败',
            content: result.error || '录音上传失败',
            showCancel: false
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('上传录音失败:', error);
        uni.showModal({
          title: '上传失败',
          content: '录音上传失败: ' + (error.message || '未知错误'),
          showCancel: false
        });
      } finally {
        this.isUploading = false;
      }
    },

    // 处理录音
    async processRecording() {
      if (!this.recordingId) return;

      try {
        this.isProcessing = true;
        this.processingProgress = 0;

        // 显示进度提示
        const progressInterval = setInterval(() => {
          if (this.processingProgress < 90) {
            this.processingProgress += Math.floor(Math.random() * 5) + 1;
          }
        }, 2000);

        // 显示处理流程提示
        uni.showLoading({
          title: '处理中...'
        });

        // 显示详细提示
        setTimeout(() => {
          if (this.isProcessing) {
            uni.showToast({
              title: '转录音频...',
              icon: 'none',
              duration: 2000
            });
          }
        }, 2000);

        setTimeout(() => {
          if (this.isProcessing) {
            uni.showToast({
              title: '识别说话人...',
              icon: 'none',
              duration: 2000
            });
          }
        }, 5000);

        // 调用处理API
        const result = await counselingService.processRecording(this.recordingId);

        // 清除进度计时器
        clearInterval(progressInterval);

        if (result.success) {
          this.processingProgress = 95;

          uni.showToast({
            title: '处理已开始',
            icon: 'success'
          });

          // 开始轮询转录结果
          this.startPollingTranscription();
        } else {
          uni.hideLoading();
          this.processingProgress = 0;

          uni.showModal({
            title: '处理失败',
            content: result.error || '录音处理失败',
            showCancel: false
          });
          this.isProcessing = false;
        }
      } catch (error) {
        uni.hideLoading();
        this.processingProgress = 0;

        console.error('处理录音失败:', error);
        uni.showModal({
          title: '处理失败',
          content: '录音处理失败: ' + error.message,
          showCancel: false
        });
        this.isProcessing = false;
      }
    },

    // 开始轮询转录结果
    startPollingTranscription() {
      // 清除之前的轮询
      if (this.transcriptionPollingTimer) {
        clearInterval(this.transcriptionPollingTimer);
      }

      // 设置轮询间隔为5秒
      this.transcriptionPollingTimer = setInterval(async () => {
        try {
          if (!this.recordingId) {
            clearInterval(this.transcriptionPollingTimer);
            return;
          }

          const result = await counselingService.getTranscription(this.recordingId);

          if (result.success) {
            // 检查转录状态
            if (result.status === 'completed') {
              // 转录完成
              clearInterval(this.transcriptionPollingTimer);
              this.processingComplete = true;
              this.isProcessing = false;

              uni.hideLoading();
              uni.showToast({
                title: '转录完成',
                icon: 'success'
              });

              if (this.onTranscriptionComplete) {
                this.onTranscriptionComplete(result);
              }

              // 转录完成后自动开始分析
              console.log('转录完成，等待5秒后开始分析...');

              // 显示等待提示
              uni.showToast({
                title: '准备分析数据...',
                icon: 'none',
                duration: 3000
              });

              setTimeout(async () => {
                try {
                  // 再次检查转录状态
                  const transcriptionResult = await counselingService.getTranscription(this.recordingId);
                  if (transcriptionResult.success && transcriptionResult.status === 'completed') {
                    console.log('转录已完成，开始分析...');
                    this.startAnalysis();
                  } else {
                    console.warn('转录尚未完成，等待更长时间...');
                    // 如果转录尚未完成，再等待5秒
                    setTimeout(() => {
                      this.startAnalysis(1); // 从重试计数1开始
                    }, 5000);
                  }
                } catch (error) {
                  console.error('检查转录状态失败:', error);
                  // 如果检查失败，仍然尝试分析
                  setTimeout(() => {
                    this.startAnalysis(1); // 从重试计数1开始
                  }, 5000);
                }
              }, 5000);
            } else if (result.status === 'failed') {
              // 转录失败
              clearInterval(this.transcriptionPollingTimer);
              this.isProcessing = false;

              uni.hideLoading();
              uni.showModal({
                title: '转录失败',
                content: result.error || '录音转录失败',
                showCancel: false
              });
            }
            // 其他状态继续轮询
          }
        } catch (error) {
          console.error('轮询转录结果失败:', error);
        }
      }, 5000);
    },

    // 播放录音
    async playRecording() {
      if (!this.audioUri) return;

      try {
        uni.showToast({
          title: '正在播放...',
          icon: 'none'
        });

        if (this.isWebAudio) {
          // 在H5环境中，使用HTML5 Audio元素播放
          const audio = new Audio(this.audioUri);
          audio.play();

          audio.onplay = () => {
            console.log('录音开始播放');
          };

          audio.onerror = (error) => {
            console.error('播放录音失败:', error);
            uni.showToast({
              title: '播放失败',
              icon: 'none'
            });
          };

          audio.onended = () => {
            console.log('录音播放结束');
          };
        } else {
          // 在非H5环境中，使用uni-app API播放录音
          const innerAudioContext = uni.createInnerAudioContext();
          innerAudioContext.src = this.audioUri;
          innerAudioContext.autoplay = true;

          innerAudioContext.onPlay(() => {
            console.log('录音开始播放');
          });

          innerAudioContext.onError((res) => {
            console.error('播放录音失败:', res);
            uni.showToast({
              title: '播放失败',
              icon: 'none'
            });
          });

          innerAudioContext.onEnded(() => {
            console.log('录音播放结束');
            // 释放资源
            innerAudioContext.destroy();
          });
        }
      } catch (error) {
        console.error('播放录音失败:', error);
        uni.showToast({
          title: '播放失败',
          icon: 'none'
        });
      }
    },

    // 获取文件大小文本
    getFileSizeText() {
      if (!this.audioBlob && !this.audioSize) {
        return '未知';
      }

      let size = this.audioSize;

      // 如果有audioBlob，使用它的大小
      if (this.audioBlob) {
        size = this.audioBlob.size;
      }

      // 转换为KB或MB
      if (size < 1024 * 1024) {
        return Math.round(size / 1024) + ' KB';
      } else {
        return (size / (1024 * 1024)).toFixed(2) + ' MB';
      }
    },

    // 重置录音
    resetRecording() {
      uni.showModal({
        title: '确认重新录制',
        content: '确定要放弃当前录音并重新开始吗？',
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 重置状态
            this.audioUri = null;
            this.audioBlob = null;
            this.audioChunks = [];
            this.recordingTime = 0;
            this.audioLevel = 0;
            this.uploadSuccess = false;
            this.recordingId = null;
            this.processingComplete = false;
            this.isProcessing = false;
            this.isAnalyzing = false;

            // 清除计时器
            if (this.transcriptionPollingTimer) {
              clearInterval(this.transcriptionPollingTimer);
              this.transcriptionPollingTimer = null;
            }
          }
        }
      });
    },

    // 开始分析
    async startAnalysis(retryCount = 0) {
      try {
        this.isAnalyzing = true;
        this.analysisProgress = 0;

        // 显示进度提示
        const progressInterval = setInterval(() => {
          if (this.analysisProgress < 90) {
            this.analysisProgress += Math.floor(Math.random() * 5) + 1;
          }
        }, 2000);

        uni.showLoading({
          title: '开始分析...'
        });

        console.log(`开始分析会话: sessionId=${this.sessionId}, 重试次数=${retryCount}`);

        // 先检查转录是否已完成
        if (this.recordingId) {
          try {
            const transcriptionResult = await counselingService.getTranscription(this.recordingId);
            if (transcriptionResult.success && transcriptionResult.status === 'completed') {
              console.log('转录已完成，继续分析...');
            } else {
              console.warn(`转录尚未完成 (状态: ${transcriptionResult.status})，等待更长时间...`);

              // 如果转录尚未完成，等待更长时间
              clearInterval(progressInterval);
              uni.hideLoading();

              uni.showToast({
                title: '等待转录完成...',
                icon: 'none',
                duration: 3000
              });

              // 延迟5秒后重试
              setTimeout(() => {
                this.startAnalysis(retryCount);
              }, 5000);

              return;
            }
          } catch (error) {
            console.error('检查转录状态失败:', error);
            // 继续尝试分析
          }
        }

        // 确保使用正确的API路径
        const result = await counselingService.analyzeDialogue(this.sessionId, {}, retryCount > 0);

        console.log(`分析API调用结果:`, result);

        // 清除进度计时器
        clearInterval(progressInterval);
        this.analysisProgress = 100;

        uni.hideLoading();

        if (result && result.success) {
          uni.showToast({
            title: '分析已开始',
            icon: 'success'
          });

          // 等待一秒后跳转
          setTimeout(() => {
            if (this.onAnalysisComplete) {
              this.onAnalysisComplete();
            }

            // 跳转到分析报告页面
            uni.navigateTo({
              url: `/pages/counseling/report?id=${this.sessionId}`
            });
          }, 1500);
        } else if (result && result.error && (
          result.error.includes('会话没有对话内容') ||
          result.error.includes('录音转录尚未完成') ||
          result.error.includes('无法获取转录内容')
        )) {
          // 如果是转录相关的错误，等待更长时间后重试
          console.warn(`分析失败，原因: ${result.error}`);

          // 显示等待提示
          uni.showToast({
            title: '等待转录完成...',
            icon: 'none',
            duration: 3000
          });

          // 延迟8秒后重试
          setTimeout(() => {
            this.startAnalysis(retryCount + 1);
          }, 8000);
        } else {
          // 如果失败且重试次数小于3，则重试
          if (retryCount < 3) {
            console.log(`分析失败，准备重试 (${retryCount + 1}/3)...`);

            // 显示重试提示
            uni.showToast({
              title: `分析重试 (${retryCount + 1}/3)`,
              icon: 'none',
              duration: 2000
            });

            // 延迟5秒后重试
            setTimeout(() => {
              this.startAnalysis(retryCount + 1);
            }, 5000);

            return;
          }

          // 显示详细错误信息
          uni.showModal({
            title: '分析失败',
            content: (result && result.error) ? result.error : '开始分析失败，请检查网络连接或联系管理员',
            showCancel: false
          });
          this.isAnalyzing = false;
        }
      } catch (error) {
        uni.hideLoading();
        console.error('开始分析失败:', error);

        // 记录更详细的错误信息
        if (error.response) {
          console.error('错误响应数据:', error.response.data);
          console.error('错误状态码:', error.response.status);
        }

        // 如果失败且重试次数小于3，则重试
        if (retryCount < 3) {
          console.log(`分析出错，准备重试 (${retryCount + 1}/3)...`);

          // 显示重试提示
          uni.showToast({
            title: `分析重试 (${retryCount + 1}/3)`,
            icon: 'none',
            duration: 2000
          });

          // 延迟5秒后重试
          setTimeout(() => {
            this.startAnalysis(retryCount + 1);
          }, 5000);

          return;
        }

        uni.showModal({
          title: '分析错误',
          content: '开始分析时发生错误: ' + (error.message || '未知错误'),
          showCancel: false
        });
        this.isAnalyzing = false;
      }
    }
  }
}
</script>

<style>
.recording-container {
  background-color: var(--bg-primary);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: var(--shadow-sm);
}

.recording-header {
  margin-bottom: 16px;
}

.recording-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.recording-content {
  display: flex;
  flex-direction: column;
}

.alert-box {
  display: flex;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.info-alert {
  background-color: #e6f7ff;
  border-left: 4px solid #1890ff;
}

.warning-alert {
  background-color: #fffbe6;
  border-left: 4px solid #fa8c16;
}

.success-alert {
  background-color: #f6ffed;
  border-left: 4px solid #52c41a;
}

.alert-icon {
  font-size: 24px;
  margin-right: 12px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.alert-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
}

.recording-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.recording-time {
  margin-bottom: 16px;
}

.time-text {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
}

.audio-level-container {
  width: 100%;
  margin-bottom: 16px;
}

.audio-level-bar {
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.audio-level-fill {
  height: 100%;
  background-color: var(--primary-color);
  transition: width 0.1s ease;
}

.button-container {
  width: 100%;
}

.record-button {
  width: 100%;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.start-button {
  background-color: var(--primary-color);
  color: white;
}

.stop-button {
  background-color: #ff4d4f;
  color: white;
}

.button-icon {
  font-size: 20px;
  margin-right: 8px;
}

.button-text {
  font-size: 16px;
  font-weight: 500;
}

.audio-player {
  width: 100%;
  margin-bottom: 16px;
}

.play-button {
  width: 100%;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background-color: #722ed1;
  color: white;
}

.button-group {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.primary-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 24px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.secondary-button {
  background-color: #f0f0f0;
  color: var(--text-primary);
  border: 1px solid #d9d9d9;
  border-radius: 24px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-button, .process-button, .analyze-button {
  width: 100%;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.upload-button {
  background-color: #1890ff;
  color: white;
}

.process-button {
  background-color: #52c41a;
  color: white;
}

.analyze-button {
  background-color: #722ed1;
  color: white;
}

.upload-button:disabled, .process-button:disabled, .analyze-button:disabled {
  background-color: #d9d9d9;
  color: #999;
}

.recording-info {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.recording-info-item {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 4px;
  display: block;
}

.process-info, .analysis-info {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.process-info-text, .analysis-info-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8px;
  display: block;
}

.process-step, .analysis-step {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 8px;
}

.step-text {
  font-size: 14px;
  color: var(--text-secondary);
}

.disclaimer {
  background-color: #fff7e6;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  border-left: 4px solid #fa8c16;
}

.disclaimer-text {
  font-size: 12px;
  color: #d46b08;
  line-height: 1.5;
}
</style>
