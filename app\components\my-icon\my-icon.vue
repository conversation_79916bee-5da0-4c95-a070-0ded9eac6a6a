<template>
  <view class="my-icon" :style="{ width: size + 'rpx', height: size + 'rpx' }">
    <image 
      :src="iconSrc" 
      :style="{ width: size + 'rpx', height: size + 'rpx' }"
      mode="aspectFit"
    ></image>
  </view>
</template>

<script>
export default {
  name: 'MyIcon',
  props: {
    type: {
      type: String,
      default: 'default'
    },
    size: {
      type: [Number, String],
      default: 24
    },
    color: {
      type: String,
      default: ''
    }
  },
  computed: {
    iconSrc() {
      // 图标映射
      const iconMap = {
        'star-filled': '/static/icons/star-filled.png',
        'vip-filled': '/static/icons/vip-filled.png',
        'chart-filled': '/static/icons/chart-filled.png',
        'wallet-filled': '/static/icons/wallet-filled.png',
        'help-filled': '/static/icons/help-filled.png',
        'right': '/static/icons/right.png',
        'back': '/static/icons/back.png',
        'checkmarkempty': '/static/icons/checkmark.png',
        'closeempty': '/static/icons/close.png',
        'download-filled': '/static/icons/download-filled.png',
        'eye-filled': '/static/icons/eye-filled.png',
        'heart-filled': '/static/icons/heart-filled.png',
        'gift-filled': '/static/icons/gift-filled.png',
        'info-filled': '/static/icons/info-filled.png',
        'search': '/static/icons/search.png',
        'paperplane-filled': '/static/icons/paperplane-filled.png',
        'safety-filled': '/static/icons/safety-filled.png',
        'arrowright': '/static/icons/arrow-right.png',
        'top': '/static/icons/top.png',
        'bottom': '/static/icons/bottom.png'
      };
      
      return iconMap[this.type] || '/static/icons/default.png';
    }
  }
};
</script>

<style>
.my-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.my-icon image {
  width: 100%;
  height: 100%;
}
</style>
