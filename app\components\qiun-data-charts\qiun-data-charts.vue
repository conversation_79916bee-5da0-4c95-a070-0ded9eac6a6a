<template>
  <view class="chartsjs-box">
    <canvas v-if="canvasId" class="charts-canvas" :id="canvasId" :canvas-id="canvasId" :style="{width: cWidth + 'px', height: cHeight + 'px'}"></canvas>
  </view>
</template>

<script>
import uCharts from './u-charts.js';

export default {
  name: 'qiun-data-charts',
  props: {
    type: {
      type: String,
      default: 'line'
    },
    canvasId: {
      type: String,
      default: 'uCharts'
    },
    chartData: {
      type: Object,
      default: () => ({
        categories: [],
        series: []
      })
    },
    opts: {
      type: Object,
      default: () => ({})
    },
    width: {
      type: Number,
      default: 0
    },
    height: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      cWidth: 375,
      cHeight: 250,
      uChartsInstance: null
    };
  },
  watch: {
    chartData: {
      handler(val) {
        this.drawCharts();
      },
      deep: true
    }
  },
  mounted() {
    this.initSize();
    this.drawCharts();
  },
  methods: {
    initSize() {
      // 获取设备信息
      const systemInfo = uni.getSystemInfoSync();
      // 如果有指定宽高则使用指定的，否则使用屏幕宽度
      this.cWidth = this.width || systemInfo.windowWidth;
      this.cHeight = this.height || 250;
    },
    drawCharts() {
      if (!this.chartData || !this.chartData.series || this.chartData.series.length === 0) {
        console.warn('No chart data available');
        return;
      }

      const ctx = uni.createCanvasContext(this.canvasId, this);

      // 合并配置
      const config = {
        type: this.type,
        context: ctx,
        width: this.cWidth,
        height: this.cHeight,
        categories: this.chartData.categories || [],
        series: this.chartData.series || [],
        animation: true,
        background: '#FFFFFF',
        padding: [15, 15, 0, 15],
        enableScroll: false,
        legend: {},
        xAxis: {
          disableGrid: true
        },
        yAxis: {},
        extra: {
          line: {
            type: 'straight'
          }
        },
        ...this.opts
      };

      if (this.uChartsInstance) {
        this.uChartsInstance.updateData(config);
      } else {
        this.uChartsInstance = new uCharts(config);
      }
    }
  }
};
</script>

<style>
.chartsjs-box {
  width: 100%;
  height: 100%;
}
.charts-canvas {
  width: 100%;
  height: 100%;
}
</style>
