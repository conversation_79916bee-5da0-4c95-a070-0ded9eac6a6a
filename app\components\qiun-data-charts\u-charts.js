/*
 * uCharts 简化版
 * 用于在 uni-app 中绘制图表
 */

class UCharts {
  constructor(opts) {
    this.opts = opts;
    this.config = {};
    this.context = opts.context;
    this.width = opts.width || 375;
    this.height = opts.height || 250;
    this.type = opts.type || 'line';
    this.categories = opts.categories || [];
    this.series = opts.series || [];
    this.animation = opts.animation !== false;
    this.background = opts.background || '#FFFFFF';
    this.padding = opts.padding || [15, 15, 0, 15];
    this.xAxis = opts.xAxis || {};
    this.yAxis = opts.yAxis || {};
    this.legend = opts.legend || {};
    this.extra = opts.extra || {};

    this.init();
  }

  init() {
    this.initConfig();
    this.drawChart();
  }

  initConfig() {
    // 初始化配置
    this.config = {
      width: this.width,
      height: this.height,
      yAxisWidth: 50,
      xAxisHeight: 30,
      padding: this.padding,
      yAxisSplit: 5,
      colors: ['#4A6FA5', '#8884d8', '#52c41a', '#faad14', '#f5222d'],
      fontFamily: '"PingFang SC", "Helvetica Neue", Arial, sans-serif'
    };

    // 计算图表区域
    this.config.chartWidth = this.width - this.config.padding[1] - this.config.padding[3] - this.config.yAxisWidth;
    this.config.chartHeight = this.height - this.config.padding[0] - this.config.padding[2] - this.config.xAxisHeight;

    // 计算Y轴范围
    let minY = 0, maxY = 0;
    if (this.series && this.series.length > 0) {
      const allData = [];
      this.series.forEach(item => {
        if (item.data && item.data.length > 0) {
          allData.push(...item.data);
        }
      });

      if (allData.length > 0) {
        minY = Math.min(...allData);
        maxY = Math.max(...allData);
      }
    }

    // 确保Y轴有合理的范围
    if (maxY === minY) {
      maxY = minY + 10;
    }

    // 设置Y轴范围
    if (this.yAxis && this.yAxis.data && this.yAxis.data.length > 0) {
      const yAxisData = this.yAxis.data[0];
      this.config.minY = yAxisData.min !== undefined ? yAxisData.min : minY;
      this.config.maxY = yAxisData.max !== undefined ? yAxisData.max : maxY;
    } else {
      this.config.minY = minY;
      this.config.maxY = maxY;
    }
  }

  drawChart() {
    if (!this.context) {
      console.error('Canvas context is not available');
      return;
    }

    // 清空画布
    this.context.clearRect(0, 0, this.width, this.height);

    // 绘制背景
    this.context.fillStyle = this.background;
    this.context.fillRect(0, 0, this.width, this.height);

    // 绘制坐标轴
    this.drawAxis();

    // 根据图表类型绘制
    switch (this.type) {
      case 'line':
        this.drawLineChart();
        break;
      case 'column':
        this.drawColumnChart();
        break;
      case 'area':
        this.drawAreaChart();
        break;
      default:
        this.drawLineChart();
    }

    // 绘制图例
    this.drawLegend();

    // 执行绘制
    this.context.draw();
  }

  drawAxis() {
    const { context: ctx, config } = this;
    const { padding, chartWidth, chartHeight, yAxisWidth, xAxisHeight, minY, maxY, fontFamily } = config;

    // 绘制背景网格
    ctx.strokeStyle = '#F0F0F0';
    ctx.lineWidth = 1;

    // 绘制水平网格线
    const yStep = chartHeight / config.yAxisSplit;
    for (let i = 0; i <= config.yAxisSplit; i++) {
      const y = padding[0] + i * yStep;

      ctx.beginPath();
      ctx.moveTo(padding[3] + yAxisWidth, y);
      ctx.lineTo(padding[3] + yAxisWidth + chartWidth, y);
      ctx.stroke();
    }

    // 绘制垂直网格线
    if (this.categories && this.categories.length > 0) {
      const step = chartWidth / (this.categories.length - 1 || 1);

      for (let i = 0; i < this.categories.length; i++) {
        const x = padding[3] + yAxisWidth + i * step;

        ctx.beginPath();
        ctx.moveTo(x, padding[0]);
        ctx.lineTo(x, this.height - padding[2] - xAxisHeight);
        ctx.stroke();
      }
    }

    // 设置坐标轴样式
    ctx.strokeStyle = '#DDDDDD';
    ctx.lineWidth = 1;

    // X轴
    ctx.beginPath();
    ctx.moveTo(padding[3] + yAxisWidth, this.height - padding[2] - xAxisHeight);
    ctx.lineTo(padding[3] + yAxisWidth + chartWidth, this.height - padding[2] - xAxisHeight);
    ctx.stroke();

    // X轴刻度和标签
    if (this.categories && this.categories.length > 0) {
      const step = chartWidth / (this.categories.length - 1 || 1);

      ctx.fillStyle = '#888888';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'top';
      ctx.font = `12px ${fontFamily}`;

      this.categories.forEach((category, index) => {
        const x = padding[3] + yAxisWidth + index * step;
        const y = this.height - padding[2] - xAxisHeight + 5;

        // 绘制标签
        ctx.fillText(category, x, y);
      });
    }

    // Y轴刻度和标签
    const valueStep = (maxY - minY) / config.yAxisSplit;

    ctx.fillStyle = '#888888';
    ctx.textAlign = 'right';
    ctx.textBaseline = 'middle';
    ctx.font = `12px ${fontFamily}`;

    for (let i = 0; i <= config.yAxisSplit; i++) {
      const y = padding[0] + i * yStep;
      const value = maxY - i * valueStep;

      // 绘制标签
      ctx.fillText(value.toFixed(0), padding[3] + yAxisWidth - 8, y);
    }
  }

  drawLineChart() {
    if (!this.series || this.series.length === 0 || !this.categories || this.categories.length === 0) {
      return;
    }

    const { context: ctx, config } = this;
    const { padding, chartWidth, chartHeight, yAxisWidth, xAxisHeight, minY, maxY, colors } = config;

    this.series.forEach((serie, serieIndex) => {
      if (!serie.data || serie.data.length === 0) {
        return;
      }

      const color = serie.color || colors[serieIndex % colors.length];
      const data = serie.data;
      const step = chartWidth / (this.categories.length - 1 || 1);

      // 计算所有点的坐标
      const points = data.map((value, index) => {
        const x = padding[3] + yAxisWidth + index * step;
        const y = padding[0] + chartHeight - ((value - minY) / (maxY - minY)) * chartHeight;
        return { x, y };
      });

      // 创建渐变
      const gradient = ctx.createLinearGradient(0, padding[0], 0, padding[0] + chartHeight);
      gradient.addColorStop(0, color);
      gradient.addColorStop(1, this.background);

      // 绘制面积
      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.moveTo(points[0].x, this.height - padding[2] - xAxisHeight);
      points.forEach((point, index) => {
        if (index === 0) {
          ctx.lineTo(point.x, point.y);
        } else {
          // 使用贝塞尔曲线使线条更平滑
          const prev = points[index - 1];
          const cpx1 = prev.x + (point.x - prev.x) / 2;
          const cpy1 = prev.y;
          const cpx2 = prev.x + (point.x - prev.x) / 2;
          const cpy2 = point.y;
          ctx.bezierCurveTo(cpx1, cpy1, cpx2, cpy2, point.x, point.y);
        }
      });
      ctx.lineTo(points[points.length - 1].x, this.height - padding[2] - xAxisHeight);
      ctx.closePath();
      ctx.globalAlpha = 0.2;
      ctx.fill();
      ctx.globalAlpha = 1;

      // 绘制线条
      ctx.strokeStyle = color;
      ctx.lineWidth = 3;
      ctx.lineJoin = 'round';
      ctx.lineCap = 'round';
      ctx.beginPath();

      points.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y);
        } else {
          // 使用贝塞尔曲线使线条更平滑
          const prev = points[index - 1];
          const cpx1 = prev.x + (point.x - prev.x) / 2;
          const cpy1 = prev.y;
          const cpx2 = prev.x + (point.x - prev.x) / 2;
          const cpy2 = point.y;
          ctx.bezierCurveTo(cpx1, cpy1, cpx2, cpy2, point.x, point.y);
        }
      });

      ctx.stroke();

      // 绘制数据点
      points.forEach((point) => {
        // 外圆
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(point.x, point.y, 5, 0, Math.PI * 2);
        ctx.fill();

        // 内圆
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(point.x, point.y, 2, 0, Math.PI * 2);
        ctx.fill();
      });
    });
  }

  drawColumnChart() {
    // 柱状图实现
    console.log('Column chart drawing not implemented');
  }

  drawAreaChart() {
    // 面积图实现
    console.log('Area chart drawing not implemented');
  }

  drawLegend() {
    if (!this.series || this.series.length === 0 || !this.legend || this.legend.show === false) {
      return;
    }

    const { context: ctx, config } = this;
    const { padding, colors, fontFamily } = config;

    ctx.font = `12px ${fontFamily}`;
    ctx.textAlign = 'left';
    ctx.textBaseline = 'middle';

    // 计算图例位置 - 居中显示
    let totalWidth = 0;
    this.series.forEach((serie, index) => {
      const name = serie.name || `系列${index + 1}`;
      totalWidth += 20 + ctx.measureText(name).width + 15; // 图标宽度 + 文本宽度 + 间距
    });
    totalWidth -= 15; // 减去最后一个间距

    let legendX = (this.width - totalWidth) / 2;
    const legendY = padding[0] / 2;

    this.series.forEach((serie, index) => {
      const color = serie.color || colors[index % colors.length];
      const name = serie.name || `系列${index + 1}`;

      // 绘制图例背景
      ctx.fillStyle = '#F8F8F8';
      ctx.strokeStyle = '#EEEEEE';
      const textWidth = ctx.measureText(name).width;
      const legendWidth = 20 + textWidth + 10;
      const legendHeight = 24;

      // 绘制圆角矩形
      ctx.beginPath();
      const radius = 12;
      ctx.moveTo(legendX + radius, legendY - legendHeight/2);
      ctx.lineTo(legendX + legendWidth - radius, legendY - legendHeight/2);
      ctx.arcTo(legendX + legendWidth, legendY - legendHeight/2, legendX + legendWidth, legendY - legendHeight/2 + radius, radius);
      ctx.lineTo(legendX + legendWidth, legendY + legendHeight/2 - radius);
      ctx.arcTo(legendX + legendWidth, legendY + legendHeight/2, legendX + legendWidth - radius, legendY + legendHeight/2, radius);
      ctx.lineTo(legendX + radius, legendY + legendHeight/2);
      ctx.arcTo(legendX, legendY + legendHeight/2, legendX, legendY + legendHeight/2 - radius, radius);
      ctx.lineTo(legendX, legendY - legendHeight/2 + radius);
      ctx.arcTo(legendX, legendY - legendHeight/2, legendX + radius, legendY - legendHeight/2, radius);
      ctx.closePath();
      ctx.fill();
      ctx.stroke();

      // 绘制图例标记 - 圆形
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.arc(legendX + 10, legendY, 5, 0, Math.PI * 2);
      ctx.fill();

      // 绘制图例文本
      ctx.fillStyle = '#555555';
      ctx.fillText(name, legendX + 20, legendY);

      legendX += legendWidth + 10; // 加上图例宽度和间距
    });
  }

  updateData(opts) {
    // 更新数据
    if (opts.categories) {
      this.categories = opts.categories;
    }

    if (opts.series) {
      this.series = opts.series;
    }

    // 重新初始化并绘制
    this.init();
  }
}

export default UCharts;
