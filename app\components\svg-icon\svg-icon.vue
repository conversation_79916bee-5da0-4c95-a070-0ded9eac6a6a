<template>
  <view class="svg-icon" :style="{ width: size + 'rpx', height: size + 'rpx' }">
    <image 
      :src="iconSrc" 
      :style="{ width: size + 'rpx', height: size + 'rpx', filter: colorFilter }"
      mode="aspectFit"
    ></image>
  </view>
</template>

<script>
export default {
  name: 'SvgIcon',
  props: {
    type: {
      type: String,
      default: 'default'
    },
    size: {
      type: [Number, String],
      default: 24
    },
    color: {
      type: String,
      default: ''
    }
  },
  computed: {
    iconSrc() {
      return `/static/icons/${this.type}.svg`;
    },
    colorFilter() {
      if (!this.color) return '';
      
      // 将颜色转换为滤镜
      // 这里简化处理，只支持一些基本颜色
      const colorMap = {
        '#fff': 'brightness(0) invert(1)',
        '#ffffff': 'brightness(0) invert(1)',
        'white': 'brightness(0) invert(1)',
        '#000': 'brightness(0) saturate(100%)',
        '#000000': 'brightness(0) saturate(100%)',
        'black': 'brightness(0) saturate(100%)',
        'red': 'invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%)',
        'blue': 'invert(8%) sepia(100%) saturate(6481%) hue-rotate(246deg) brightness(89%) contrast(144%)',
        'green': 'invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg) brightness(118%) contrast(119%)',
        'yellow': 'invert(73%) sepia(60%) saturate(1433%) hue-rotate(359deg) brightness(100%) contrast(106%)',
        'orange': 'invert(65%) sepia(54%) saturate(2651%) hue-rotate(346deg) brightness(100%) contrast(104%)',
        'purple': 'invert(21%) sepia(90%) saturate(5205%) hue-rotate(281deg) brightness(81%) contrast(113%)',
        'pink': 'invert(73%) sepia(42%) saturate(7497%) hue-rotate(300deg) brightness(101%) contrast(103%)',
        'gray': 'invert(50%) sepia(0%) saturate(0%) hue-rotate(153deg) brightness(94%) contrast(92%)',
        'grey': 'invert(50%) sepia(0%) saturate(0%) hue-rotate(153deg) brightness(94%) contrast(92%)',
      };
      
      return colorMap[this.color.toLowerCase()] || '';
    }
  }
};
</script>

<style>
.svg-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.svg-icon image {
  width: 100%;
  height: 100%;
}
</style>
