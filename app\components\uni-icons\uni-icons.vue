<template>
  <text :style="{ color: color, 'font-size': size + 'px' }" class="uni-icons">{{ iconUnicode }}</text>
</template>

<script>
export default {
  name: 'UniIcons',
  props: {
    type: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: '#333333'
    },
    size: {
      type: [Number, String],
      default: 16
    }
  },
  computed: {
    iconUnicode() {
      // 简单的图标映射
      const iconMap = {
        'back': '\ue6db',
        'right': '\ue6e0',
        'top': '\ue6d6',
        'bottom': '\ue6d7',
        'star-filled': '\ue68f',
        'star': '\ue688',
        'download-filled': '\ue681',
        'download': '\ue68d',
        'eye-filled': '\ue66a',
        'eye': '\ue651',
        'headphones-filled': '\ue630',
        'headphones': '\ue62e',
        'heart-filled': '\ue641',
        'heart': '\ue63e',
        'gift-filled': '\ue6c4',
        'gift': '\ue6c5',
        'info-filled': '\ue649',
        'info': '\ue64a',
        'checkmarkempty': '\ue65c',
        'closeempty': '\ue65c',
        'search': '\ue654',
        'wallet-filled': '\ue6c2',
        'wallet': '\ue6c3',
        'paperplane-filled': '\ue67a',
        'paperplane': '\ue672',
        'safety-filled': '\ue6c7',
        'safety': '\ue6c8',
        'help-filled': '\ue674',
        'help': '\ue679',
        'vip-filled': '\ue6c6',
        'vip': '\ue6c9',
        'chart-filled': '\ue6cc',
        'chart': '\ue6cd',
        'person-filled': '\ue69d',
        'person': '\ue699',
        'arrowright': '\ue6d4',
        'closeempty': '\ue65c'
      };
      
      return iconMap[this.type] || '\ue600'; // 默认图标
    }
  }
};
</script>

<style>
@font-face {
  font-family: uniicons;
  font-weight: normal;
  font-style: normal;
  src: url('https://at.alicdn.com/t/font_2210089_emznjbovnj.ttf') format('truetype');
}

.uni-icons {
  font-family: uniicons;
  text-decoration: none;
  text-align: center;
}
</style>
