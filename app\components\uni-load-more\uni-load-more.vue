<template>
  <view class="uni-load-more">
    <view v-if="status === 'loading'" class="uni-load-more__img">
      <view class="loading-icon"></view>
    </view>
    <text class="uni-load-more__text">
      {{ statusText }}
    </text>
  </view>
</template>

<script>
export default {
  name: 'UniLoadMore',
  props: {
    status: {
      // 上拉的状态：more-loading前；loading-loading中；noMore-没有更多了
      type: String,
      default: 'more'
    },
    contentText: {
      type: Object,
      default() {
        return {
          contentdown: '上拉显示更多',
          contentrefresh: '正在加载...',
          contentnomore: '没有更多数据了'
        };
      }
    }
  },
  computed: {
    statusText() {
      if (this.status === 'more') {
        return this.contentText.contentdown;
      } else if (this.status === 'loading') {
        return this.contentText.contentrefresh;
      } else {
        return this.contentText.contentnomore;
      }
    }
  }
};
</script>

<style>
.uni-load-more {
  display: flex;
  flex-direction: row;
  height: 40px;
  align-items: center;
  justify-content: center;
}

.uni-load-more__text {
  font-size: 14px;
  color: #999;
}

.uni-load-more__img {
  margin-right: 8px;
}

.loading-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #999;
  border-top-color: transparent;
  animation: loading 1s linear infinite;
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
