<template>
  <view class="uni-popup" v-if="showPopup">
    <view class="uni-popup__mask" @click="close"></view>
    <view class="uni-popup__wrapper" :class="['uni-popup__wrapper-' + type]">
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UniPopup',
  props: {
    // 开启动画
    animation: {
      type: Boolean,
      default: true
    },
    // 弹出层类型，可选值，top: 顶部弹出层；bottom：底部弹出层；center：全屏弹出层
    type: {
      type: String,
      default: 'center'
    },
    // 是否显示遮罩
    mask: {
      type: Boolean,
      default: true
    },
    // 点击遮罩是否关闭弹窗
    maskClick: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showPopup: false
    };
  },
  methods: {
    open() {
      this.showPopup = true;
      this.$emit('change', {
        show: true
      });
    },
    close() {
      if (this.maskClick) {
        this.showPopup = false;
        this.$emit('change', {
          show: false
        });
      }
    }
  }
};
</script>

<style>
.uni-popup {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
}

.uni-popup__mask {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.4);
}

.uni-popup__wrapper {
  position: absolute;
  background-color: #fff;
}

.uni-popup__wrapper-center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 80%;
  border-radius: 10px;
  display: flex;
  justify-content: center;
}

.uni-popup__wrapper-top {
  top: 0;
  left: 0;
  width: 100%;
}

.uni-popup__wrapper-bottom {
  bottom: 0;
  left: 0;
  width: 100%;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
</style>
