import { API_BASE_URL } from '../config/appConfig';

/**
 * 心理咨询服务
 * 提供与心理咨询相关的API调用
 */
const counselingService = {
  /**
   * 获取API基础URL
   * @returns {string} - API基础URL
   */
  getApiBaseUrl: () => {
    return API_BASE_URL;
  },
  /**
   * 获取API基础URL
   * @returns {string} - API基础URL
   */
  getApiBaseUrl: () => {
    return API_BASE_URL;
  },
  /**
   * 获取用户的心理咨询会话列表
   * @param {string} role - 角色，可选值：all, therapist, client
   * @returns {Promise} - 操作结果
   */
  getSessions: async (role = 'all') => {
    try {
      console.log(`开始获取会话列表: role=${role}`);

      // 从本地存储获取token，只使用标准键名
      const token = uni.getStorageSync('access_token');

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json'
      };

      // 如果有token，添加到请求头
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await uni.request({
        url: `${API_BASE_URL}/counseling/sessions?role=${role}`,
        method: 'GET',
        header: headers
      });

      // 处理响应
      if (response.statusCode >= 200 && response.statusCode < 300) {
        const result = response.data;

      if (result && Array.isArray(result)) {
        // 处理会话数据，确保分析状态和风险等级正确
        const sessionsWithAnalysis = result.map(session => {
          if (session.analysis) {
            // 确保分析状态是字符串
            if (typeof session.analysis.status !== 'string') {
              session.analysis.status = String(session.analysis.status);
            }

            // 确保分析完成的会话有风险等级
            if (session.analysis.status === 'completed' && !session.analysis.risk_level) {
              console.log(`会话 ID: ${session.id} 分析已完成但没有风险等级，设置默认风险等级: low`);
              session.analysis.risk_level = 'low';
            }
          }
          return session;
        });

        return { success: true, data: sessionsWithAnalysis };
      }

      return { success: true, data: result || [] };
      } else {
        return {
          success: false,
          error: response.data?.error || `请求失败，状态码: ${response.statusCode}`
        };
      }
    } catch (error) {
      console.error('获取心理咨询会话列表失败:', error);
      return { success: false, error: error.errMsg || error.message || '获取会话列表失败' };
    }
  },

  /**
   * 获取会话详情
   * @param {number|string} sessionId - 会话ID
   * @returns {Promise} - 操作结果
   */
  getSession: async (sessionId) => {
    try {
      // 从本地存储获取token
      const token = uni.getStorageSync('access_token');

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json'
      };

      // 如果有token，添加到请求头
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await uni.request({
        url: `${API_BASE_URL}/counseling/sessions/${sessionId}`,
        method: 'GET',
        header: headers
      });

      // 处理响应
      if (response.statusCode >= 200 && response.statusCode < 300) {
        // 确保会话对象包含必要字段
        const sessionData = {
          status: 'new', // 默认状态
          recordings: [], // 默认空数组
          ...response.data // 覆盖默认值
        };

        // 标准化recordings字段
        if (!sessionData.recordings) {
          sessionData.recordings = [];
        } else if (!Array.isArray(sessionData.recordings)) {
          sessionData.recordings = [sessionData.recordings];
        }

        // 记录状态检查
        console.log(`标准化后的会话状态: ${sessionData.status}`);

        // 记录会话中的录音信息
        if (sessionData && sessionData.recordings) {
          console.log(`会话录音数量: ${sessionData.recordings.length}`);
        }

        return { success: true, data: sessionData };
      } else {
        return {
          success: false,
          error: response.data?.error || `请求失败，状态码: ${response.statusCode}`
        };
      }
    } catch (error) {
      console.error(`获取会话详情失败: sessionId=${sessionId}`, error);
      return { success: false, error: error.errMsg || error.message || '获取会话详情失败' };
    }
  },

  /**
   * 创建新的咨询会话
   * @param {Object} sessionData - 会话数据
   * @returns {Promise} - 操作结果
   */
  createSession: async (sessionData) => {
    try {
      // 从本地存储获取token
      const token = uni.getStorageSync('access_token');

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json'
      };

      // 如果有token，添加到请求头
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await uni.request({
        url: `${API_BASE_URL}/counseling/sessions`,
        method: 'POST',
        data: sessionData,
        header: headers
      });

      // 处理响应
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return { success: true, ...response.data };
      } else {
        return {
          success: false,
          error: response.data?.error || `请求失败，状态码: ${response.statusCode}`
        };
      }
    } catch (error) {
      console.error('创建咨询会话失败:', error);
      return { success: false, error: error.errMsg || error.message || '创建会话失败' };
    }
  },

  /**
   * 更新会话信息
   * @param {number|string} sessionId - 会话ID
   * @param {Object} sessionData - 会话数据
   * @returns {Promise} - 操作结果
   */
  updateSession: async (sessionId, sessionData) => {
    try {
      // 从本地存储获取token
      const token = uni.getStorageSync('access_token');

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json'
      };

      // 如果有token，添加到请求头
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await uni.request({
        url: `${API_BASE_URL}/counseling/sessions/${sessionId}`,
        method: 'PUT',
        data: sessionData,
        header: headers
      });

      // 处理响应
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return { success: true, data: response.data };
      } else {
        return {
          success: false,
          error: response.data?.error || `请求失败，状态码: ${response.statusCode}`
        };
      }
    } catch (error) {
      console.error(`更新会话失败: sessionId=${sessionId}`, error);
      return { success: false, error: error.errMsg || error.message || '更新会话失败' };
    }
  },

  /**
   * 删除会话
   * @param {number|string} sessionId - 会话ID
   * @returns {Promise} - 操作结果
   */
  deleteSession: async (sessionId) => {
    try {
      // 从本地存储获取token
      const token = uni.getStorageSync('access_token');

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json'
      };

      // 如果有token，添加到请求头
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await uni.request({
        url: `${API_BASE_URL}/counseling/sessions/${sessionId}`,
        method: 'DELETE',
        header: headers
      });

      // 处理响应
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return { success: true, data: response.data };
      } else {
        return {
          success: false,
          error: response.data?.error || `请求失败，状态码: ${response.statusCode}`
        };
      }
    } catch (error) {
      console.error(`删除会话失败: sessionId=${sessionId}`, error);
      return { success: false, error: error.errMsg || error.message || '删除会话失败' };
    }
  },

  /**
   * 分析对话内容
   * @param {number|string} sessionId - 会话ID
   * @param {Object} additionalInfo - 附加信息
   * @param {boolean} forceReanalyze - 是否强制重新分析
   * @param {boolean} reprocessAudio - 是否重新处理音频，默认为false避免重复处理
   * @returns {Promise} - 操作结果
   */
  analyzeDialogue: async (sessionId, additionalInfo = {}, forceReanalyze = false, reprocessAudio = false) => {
    try {
      console.log(`调用分析API: sessionId=${sessionId}, forceReanalyze=${forceReanalyze}, reprocessAudio=${reprocessAudio}`);

      // 从本地存储获取token
      const token = uni.getStorageSync('access_token');

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json'
      };

      // 如果有token，添加到请求头
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const requestData = {
        session_id: sessionId,
        additional_info: additionalInfo,
        force_reanalyze: forceReanalyze,
        reprocess_audio: reprocessAudio
      };

      console.log(`分析请求数据:`, requestData);
      console.log(`API基础URL: ${API_BASE_URL}`);

      // 确保使用正确的API路径
      const apiUrl = `${API_BASE_URL}/counseling/analyze`;
      console.log(`完整API URL: ${apiUrl}`);

      // 使用Promise包装uni.request，以便更好地处理错误
      const response = await new Promise((resolve, reject) => {
        uni.request({
          url: apiUrl,
          method: 'POST',
          data: requestData,
          header: headers,
          timeout: 30000, // 增加超时时间到30秒
          success: (res) => {
            console.log(`分析API响应状态码: ${res.statusCode}`);
            console.log(`分析API响应数据:`, res.data);
            resolve(res);
          },
          fail: (err) => {
            console.error(`分析API请求失败:`, err);
            reject(err);
          }
        });
      });

      // 处理响应
      if (response.statusCode >= 200 && response.statusCode < 300) {
        // 确保响应数据包含success字段
        if (response.data && !response.data.hasOwnProperty('success')) {
          response.data.success = true;
        }
        return { success: true, ...response.data };
      } else {
        return {
          success: false,
          error: response.data?.error || `请求失败，状态码: ${response.statusCode}`
        };
      }
    } catch (error) {
      console.error(`分析对话内容失败: sessionId=${sessionId}`, error);
      // 记录更详细的错误信息
      if (error.errMsg) {
        console.error(`错误信息: ${error.errMsg}`);
      }
      return { success: false, error: error.errMsg || error.message || '分析对话失败' };
    }
  },

  /**
   * 提交会话评价
   * @param {number|string} sessionId - 会话ID
   * @param {Object} ratingData - 评价数据
   * @returns {Promise} - 操作结果
   */
  submitRating: async (sessionId, ratingData) => {
    try {
      // 从本地存储获取token
      const token = uni.getStorageSync('access_token');

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json'
      };

      // 如果有token，添加到请求头
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await uni.request({
        url: `${API_BASE_URL}/counseling/sessions/${sessionId}/rating`,
        method: 'POST',
        data: ratingData,
        header: headers
      });

      // 处理响应
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return { success: true, data: response.data };
      } else {
        return {
          success: false,
          error: response.data?.error || `请求失败，状态码: ${response.statusCode}`
        };
      }
    } catch (error) {
      console.error(`提交会话评价失败: sessionId=${sessionId}`, error);
      return { success: false, error: error.errMsg || error.message || '提交评价失败' };
    }
  },

  /**
   * 上传心理咨询录音
   * @param {string} audioUri - 音频文件URI
   * @param {number|string} sessionId - 会话ID
   * @param {number} duration - 录音时长（秒）
   * @returns {Promise} - 操作结果
   */
  uploadRecording: async (audioUri, sessionId, duration = null) => {
    try {
      console.log(`开始上传录音: sessionId=${sessionId}, audioUri=${audioUri}, duration=${duration}秒`);

      // 从本地存储获取token
      const token = uni.getStorageSync('access_token');

      // 构建表单数据
      const formData = {
        session_id: sessionId.toString()
      };

      // 如果有录音时长，添加到表单数据中
      if (duration !== null && duration > 0) {
        formData.duration = duration.toString();
      }

      // 发送上传请求
      const response = await uni.uploadFile({
        url: `${API_BASE_URL}/counseling/recordings`,
        filePath: audioUri,
        name: 'audio',
        formData: formData,
        header: token ? { 'Authorization': `Bearer ${token}` } : {}
      });

      // 处理响应
      if (response.statusCode >= 200 && response.statusCode < 300) {
        const data = JSON.parse(response.data);
        console.log('录音上传成功:', data);
        return { success: true, ...data };
      } else {
        let errorData = {};
        try {
          errorData = JSON.parse(response.data);
        } catch (e) {
          console.error('解析响应数据失败:', e);
        }

        return {
          success: false,
          error: errorData.error || `请求失败，状态码: ${response.statusCode}`
        };
      }
    } catch (error) {
      console.error('上传录音失败:', error);
      return { success: false, error: error.errMsg || error.message || '上传录音失败' };
    }
  },

  /**
   * 处理录音（转录和说话人识别）
   * @param {number|string} recordingId - 录音ID
   * @returns {Promise} - 操作结果
   */
  processRecording: async (recordingId) => {
    try {
      console.log(`开始处理录音: recordingId=${recordingId}`);

      // 从本地存储获取token
      const token = uni.getStorageSync('access_token');

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json'
      };

      // 如果有token，添加到请求头
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await uni.request({
        url: `${API_BASE_URL}/counseling/recordings/${recordingId}/process`,
        method: 'POST',
        header: headers
      });

      // 处理响应
      if (response.statusCode >= 200 && response.statusCode < 300) {
        console.log(`录音处理请求成功: recordingId=${recordingId}`, response.data);
        return { success: true, ...response.data };
      } else {
        return {
          success: false,
          error: response.data?.error || `请求失败，状态码: ${response.statusCode}`
        };
      }
    } catch (error) {
      console.error(`处理录音失败: recordingId=${recordingId}`, error);
      return { success: false, error: error.errMsg || error.message || '处理录音失败' };
    }
  },

  /**
   * 获取转录结果
   * @param {number|string} recordingId - 录音ID
   * @returns {Promise} - 操作结果
   */
  getTranscription: async (recordingId) => {
    try {
      // 从本地存储获取token
      const token = uni.getStorageSync('access_token');

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json'
      };

      // 如果有token，添加到请求头
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await uni.request({
        url: `${API_BASE_URL}/counseling/recordings/${recordingId}/transcription`,
        method: 'GET',
        header: headers
      });

      // 处理响应
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return { success: true, ...response.data };
      } else {
        return {
          success: false,
          error: response.data?.error || `请求失败，状态码: ${response.statusCode}`
        };
      }
    } catch (error) {
      console.error('获取转录结果失败:', error);
      return { success: false, error: error.errMsg || error.message || '获取转录结果失败' };
    }
  },

  /**
   * 获取会话的录音记录
   * @param {number|string} sessionId - 会话ID
   * @returns {Promise} - 操作结果
   */
  getSessionRecordings: async (sessionId) => {
    try {
      // 从本地存储获取token
      const token = uni.getStorageSync('access_token');

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json'
      };

      // 如果有token，添加到请求头
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await uni.request({
        url: `${API_BASE_URL}/counseling/sessions/${sessionId}/recordings`,
        method: 'GET',
        header: headers
      });

      // 处理响应
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return { success: true, ...response.data };
      } else {
        return {
          success: false,
          error: response.data?.error || `请求失败，状态码: ${response.statusCode}`
        };
      }
    } catch (error) {
      console.error('获取会话录音记录失败:', error);
      return { success: false, error: error.errMsg || error.message || '获取会话录音记录失败' };
    }
  },

  /**
   * 获取会话分析状态和报告
   * @param {number|string} sessionId - 会话ID
   * @returns {Promise} - 操作结果
   */
  getSessionAnalysis: async (sessionId) => {
    try {
      console.log(`开始获取会话分析状态: sessionId=${sessionId}`);

      // 从本地存储获取token
      const token = uni.getStorageSync('access_token');

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json'
      };

      // 如果有token，添加到请求头
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      console.log(`发送请求到: ${API_BASE_URL}/counseling/sessions/${sessionId}/analysis`);

      const response = await uni.request({
        url: `${API_BASE_URL}/counseling/sessions/${sessionId}/analysis`,
        method: 'GET',
        header: headers
      });

      console.log(`分析状态API响应状态码: ${response.statusCode}`);

      // 处理响应
      if (response.statusCode >= 200 && response.statusCode < 300) {
        console.log(`分析状态API响应数据:`, response.data);

        // 检查响应数据中的关键字段
        if (response.data) {
          console.log(`分析状态: ${response.data.status || '未知'}`);
          console.log(`风险等级: ${response.data.risk_level || '未知'}`);
          console.log(`是否有报告内容: ${response.data.report_content ? '是' : '否'}`);
          console.log(`是否有分析结果: ${response.data.analysis_results ? '是' : '否'}`);
        }

        return response.data;
      } else {
        console.error(`获取分析状态失败，状态码: ${response.statusCode}`, response.data);
        return {
          error: response.data?.error || `请求失败，状态码: ${response.statusCode}`
        };
      }
    } catch (error) {
      console.error('获取会话分析状态失败:', error);
      return { error: error.errMsg || error.message || '获取会话分析状态失败' };
    }
  },

  /**
   * 检查会话是否有对话记录
   * @param {number|string} sessionId - 会话ID
   * @returns {Promise} - 操作结果
   */
  getSessionDialogueStatus: async (sessionId) => {
    try {
      console.log(`开始检查会话对话状态: sessionId=${sessionId}`);

      // 从本地存储获取token
      const token = uni.getStorageSync('access_token');

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json'
      };

      // 如果有token，添加到请求头
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await uni.request({
        url: `${API_BASE_URL}/counseling/sessions/${sessionId}/dialogue-status`,
        method: 'GET',
        header: headers
      });

      // 处理响应
      if (response.statusCode >= 200 && response.statusCode < 300) {
        console.log(`对话状态检查结果:`, response.data);
        return { success: true, ...response.data };
      } else {
        return {
          success: false,
          error: response.data?.error || `请求失败，状态码: ${response.statusCode}`
        };
      }
    } catch (error) {
      console.error('检查会话对话状态失败:', error);
      return { success: false, error: error.errMsg || error.message || '检查会话对话状态失败' };
    }
  },

  /**
   * 删除指定会话的所有录音记录
   * @param {number|string} sessionId - 会话ID
   * @returns {Promise} - 操作结果
   */
  deleteSessionRecordings: async (sessionId) => {
    try {
      console.log(`开始删除会话录音: sessionId=${sessionId}`);

      // 从本地存储获取token
      const token = uni.getStorageSync('access_token');

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json'
      };

      // 如果有token，添加到请求头
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await uni.request({
        url: `${API_BASE_URL}/counseling/sessions/${sessionId}/recordings`,
        method: 'DELETE',
        header: headers
      });

      // 处理响应
      if (response.statusCode >= 200 && response.statusCode < 300) {
        console.log(`删除会话录音成功: sessionId=${sessionId}`, response.data);
        // 后端返回的数据结构可能包含 success: true 和 message
        return { success: true, ...response.data };
      } else {
        console.error(`删除会话录音失败，状态码: ${response.statusCode}`, response.data);
        return {
          success: false,
          error: response.data?.detail || response.data?.error || `请求失败，状态码: ${response.statusCode}`
        };
      }
    } catch (error) {
      console.error(`删除会话录音时出错: sessionId=${sessionId}`, error);
      return { success: false, error: error.errMsg || error.message || '删除会话录音时出错' };
    }
  },

  /**
   * 下载咨询报告为Word文档
   * @param {number|string} sessionId - 会话ID
   * @returns {Promise<Object>} - 下载结果对象
   */
  downloadReportAsDocx: async (sessionId) => {
    try {
      // 验证会话ID
      if (!sessionId || isNaN(sessionId) || sessionId <= 0) {
        console.error('无效的会话ID:', sessionId);
        throw new Error(`无效的会话ID: ${sessionId}`);
      }

      // 确保sessionId是整数
      const validSessionId = parseInt(sessionId);
      console.log(`开始下载咨询报告: sessionId=${validSessionId}`);

      // 从本地存储获取token
      const token = uni.getStorageSync('access_token');

      // 返回一个Promise，封装下载过程
      return new Promise((resolve, reject) => {
        uni.downloadFile({
          url: `${API_BASE_URL}/reports/counseling/${validSessionId}/export/docx`,
          header: {
            'Authorization': `Bearer ${token}`
          },
          success: (res) => {
            console.log('下载成功:', res);
            resolve(res);
          },
          fail: (err) => {
            console.error('下载失败:', err);
            reject(err);
          }
        });
        // 注意：让下载完成后通过success或fail回调来resolve/reject
      });
    } catch (error) {
      console.error('下载咨询报告失败:', error);
      throw error; // 重新抛出错误，让调用者处理
    }
  },

  /**
   * 分享咨询报告到微信或使用系统分享
   * @param {string} filePath - 文件路径
   * @param {string} fileName - 文件名称
   * @returns {Promise<boolean>} - 分享结果
   */
  shareReportToWeChat: async (filePath, fileName) => {
    try {
      console.log(`准备分享文件: ${filePath}`);

      // 检查平台
      const systemInfo = uni.getSystemInfoSync();
      console.log('当前系统信息:', systemInfo);

      // #ifdef APP-PLUS
      // 直接使用系统分享，不再检查微信是否安装
      console.log('使用系统分享功能');

      return new Promise((resolve, reject) => {
        try {
          // 使用plus.share分享文件
          plus.share.sendWithSystem({
            type: 'file',
            title: '心理咨询报告',
            href: filePath,
            content: '我的心理咨询报告',
            success: function() {
              console.log('分享系统选择器已显示');
              resolve(true);
            },
            fail: function(err) {
              console.error('调用系统分享失败:', err);
              reject(err);
            }
          });
        } catch (innerError) {
          console.error('调用系统分享出错:', innerError);
          // 即使出错也要resolve，避免UI卡在加载状态
          resolve(false);
        }
      });
      // #endif

      // #ifdef H5 || MP
      console.log('当前平台不支持此分享方式');
      return Promise.resolve(false); // 返回一个已解决的Promise，而不是抛出错误
      // #endif
    } catch (error) {
      console.error('分享失败:', error);
      // 返回一个已解决的Promise，而不是抛出错误，避免UI卡在加载状态
      return Promise.resolve(false);
    }
  }
};

export default counselingService;