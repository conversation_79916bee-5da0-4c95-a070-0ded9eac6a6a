
.uni-popup[data-v-c9f9675a] {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
}
.uni-popup__mask[data-v-c9f9675a] {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.4);
}
.uni-popup__wrapper[data-v-c9f9675a] {
  position: absolute;
  background-color: #fff;
}
.uni-popup__wrapper-center[data-v-c9f9675a] {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 80%;
  border-radius: 10px;
  display: flex;
  justify-content: center;
}
.uni-popup__wrapper-top[data-v-c9f9675a] {
  top: 0;
  left: 0;
  width: 100%;
}
.uni-popup__wrapper-bottom[data-v-c9f9675a] {
  bottom: 0;
  left: 0;
  width: 100%;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}


/* ===== 全局样式 ===== */
.counseling-page[data-v-842d416b] {
  min-height: 100vh;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

/* ===== 页面头部 ===== */
.page-header[data-v-842d416b] {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 16px 16px 12px;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
}
.header-content[data-v-842d416b] {
  max-width: 1200px;
  margin: 0 auto;
}
.title-section[data-v-842d416b] {
  text-align: center;
  margin-bottom: 12px;
}
.page-title[data-v-842d416b] {
  font-size: 22px;
  font-weight: 600;
  color: #2c3e50;
  display: block;
}
.action-section[data-v-842d416b] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

/* 筛选器组 */
.filter-group[data-v-842d416b] {
  flex: 0 0 auto;
}
.role-picker[data-v-842d416b] {
  width: auto;
}
.picker-display[data-v-842d416b] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 5px 8px;
  min-width: 60px;
  max-width: 80px;
}
.picker-text[data-v-842d416b] {
  font-size: 11px;
  color: #495057;
  font-weight: 500;
}
.picker-arrow[data-v-842d416b] {
  font-size: 9px;
  color: #6c757d;
  margin-left: 4px;
}

/* 按钮组 */
.button-group[data-v-842d416b] {
  display: flex;
  gap: 6px;
}
.action-btn[data-v-842d416b] {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 4px 8px;
  border-radius: 5px;
  border: none;
  font-size: 10px;
  font-weight: 500;
  transition: all 0.2s ease;
}
.refresh-btn[data-v-842d416b] {
  background: #e3f2fd;
  color: #1976d2;
}
.refresh-btn[data-v-842d416b]:active {
  background: #bbdefb;
}
.info-btn[data-v-842d416b] {
  background: #f3e5f5;
  color: #7b1fa2;
}
.info-btn[data-v-842d416b]:active {
  background: #e1bee7;
}
.btn-icon[data-v-842d416b] {
  font-size: 12px;
}
.btn-text[data-v-842d416b] {
  font-size: 10px;
}

/* ===== 内容区域 ===== */
.content-container[data-v-842d416b] {
  flex: 1;
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

/* 加载状态 */
.loading-container[data-v-842d416b] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6c757d;
}
.loading-spinner[data-v-842d416b] {
  width: 40px;
  height: 40px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin-842d416b 1s linear infinite;
  margin-bottom: 16px;
}
@keyframes spin-842d416b {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}

/* 空状态 */
.empty-state[data-v-842d416b] {
  text-align: center;
  padding: 80px 20px;
  color: #6c757d;
}
.empty-icon[data-v-842d416b] {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}
.empty-title[data-v-842d416b] {
  font-size: 18px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  display: block;
}
.empty-subtitle[data-v-842d416b] {
  font-size: 14px;
  color: #6c757d;
  display: block;
}

/* ===== 会话列表 ===== */
.sessions-container[data-v-842d416b] {
  width: 100%;
}
.sessions-list[data-v-842d416b] {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 会话卡片 */
.session-card[data-v-842d416b] {
  background: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}
.session-card[data-v-842d416b]:active {
  transform: translateY(1px);
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.12);
}

/* 卡片头部 */
.card-header[data-v-842d416b] {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 16px 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.header-left[data-v-842d416b] {
  flex: 1;
}
.title-row[data-v-842d416b] {
  display: flex;
  align-items: baseline;
  gap: 8px;
  flex-wrap: wrap;
}
.session-title[data-v-842d416b] {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  flex-shrink: 0;
}
.session-id[data-v-842d416b] {
  font-size: 12px;
  color: #7f8c8d;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  flex-shrink: 0;
}
.header-right[data-v-842d416b] {
  margin-left: 12px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}
.analysis-badges[data-v-842d416b] {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: flex-end;
}
.status-badge[data-v-842d416b] {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

/* 右上角的分析状态和风险等级徽章 */
.analysis-badges .analysis-badge[data-v-842d416b],
.analysis-badges .risk-badge[data-v-842d416b] {
  padding: 3px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
}

/* 状态样式 */
.status-scheduled[data-v-842d416b] {
  background: #e3f2fd;
  color: #1976d2;
}
.status-in-progress[data-v-842d416b] {
  background: #fff3e0;
  color: #f57c00;
}
.status-completed[data-v-842d416b] {
  background: #e8f5e8;
  color: #2e7d32;
}
.status-cancelled[data-v-842d416b] {
  background: #ffebee;
  color: #d32f2f;
}

/* 卡片内容 */
.card-content[data-v-842d416b] {
  padding: 16px;
}
.info-row[data-v-842d416b] {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}
.info-row[data-v-842d416b]:last-child {
  margin-bottom: 0;
}
.info-item[data-v-842d416b] {
  flex: 1;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  min-width: 0;
}
.info-icon[data-v-842d416b] {
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}
.info-text[data-v-842d416b] {
  flex: 1;
  min-width: 0;
}
.info-label[data-v-842d416b] {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 2px;
  display: block;
}
.info-value[data-v-842d416b] {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
  display: block;
  word-break: break-all;
}
.time-text[data-v-842d416b] {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  font-size: 13px;
}

/* 风险等级和分析状态徽章 */
.risk-badge[data-v-842d416b], .analysis-badge[data-v-842d416b] {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
}
.risk-low[data-v-842d416b] {
  background: #e8f5e8;
  color: #2e7d32;
}
.risk-medium[data-v-842d416b] {
  background: #fff3e0;
  color: #f57c00;
}
.risk-high[data-v-842d416b] {
  background: #ffebee;
  color: #d32f2f;
}
.risk-critical[data-v-842d416b] {
  background: #fce4ec;
  color: #c2185b;
}
.status-pending[data-v-842d416b] {
  background: #f3e5f5;
  color: #7b1fa2;
}
.status-processing[data-v-842d416b] {
  background: #e3f2fd;
  color: #1976d2;
}
.status-completed[data-v-842d416b] {
  background: #e8f5e8;
  color: #2e7d32;
}
.status-failed[data-v-842d416b] {
  background: #ffebee;
  color: #d32f2f;
}

/* 描述行 */
.description-row[data-v-842d416b] {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}
.description-icon[data-v-842d416b] {
  font-size: 14px;
  margin-top: 2px;
  flex-shrink: 0;
  color: #6c757d;
}
.description-text[data-v-842d416b] {
  flex: 1;
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
}

/* ===== 弹窗样式 ===== */
.popup-container[data-v-842d416b] {
  background: white;
  border-radius: 20px;
  padding: 24px;
  width: 280px;
  max-width: 280px;
  margin: 0 auto;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  box-sizing: border-box;
}
.popup-header[data-v-842d416b] {
  text-align: center;
  margin-bottom: 20px;
}
.popup-title[data-v-842d416b] {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 4px;
  display: block;
}
.popup-subtitle[data-v-842d416b] {
  font-size: 14px;
  color: #7f8c8d;
  display: block;
}
.popup-content[data-v-842d416b] {
  margin-bottom: 24px;
}
.feature-item[data-v-842d416b] {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}
.feature-item[data-v-842d416b]:last-child {
  margin-bottom: 0;
}
.feature-icon[data-v-842d416b] {
  font-size: 20px;
  flex-shrink: 0;
}
.feature-text[data-v-842d416b] {
  flex: 1;
  font-size: 14px;
  color: #495057;
  line-height: 1.4;
}
.popup-footer[data-v-842d416b] {
  text-align: center;
}
.disclaimer[data-v-842d416b] {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 16px;
  display: block;
  font-style: italic;
}
.popup-btn[data-v-842d416b] {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}
.popup-btn[data-v-842d416b]:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.4);
}

/* ===== 悬浮按钮 ===== */
.fab-container[data-v-842d416b] {
  position: fixed;
  bottom: 30px;
  right: 20px;
  z-index: 1000;
}
.fab-button[data-v-842d416b] {
  display: flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 10px 16px;
  font-size: 13px;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}
.fab-button[data-v-842d416b]:active {
  transform: translateY(1px);
  box-shadow: 0 2px 12px rgba(102, 126, 234, 0.3);
}
.fab-icon[data-v-842d416b] {
  font-size: 16px;
  font-weight: 300;
}
.fab-text[data-v-842d416b] {
  font-size: 13px;
  font-weight: 600;
}
