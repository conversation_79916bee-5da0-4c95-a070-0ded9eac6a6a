
@font-face {
  font-family: uniicons;
  font-weight: normal;
  font-style: normal;
  src: url('https://at.alicdn.com/t/font_2210089_emznjbovnj.ttf') format('truetype');
}
.uni-icons[data-v-f218fb61] {
  font-family: uniicons;
  text-decoration: none;
  text-align: center;
}


.svg-icon[data-v-e9423230] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.svg-icon uni-image[data-v-e9423230] {
  width: 100%;
  height: 100%;
}


.uni-popup[data-v-c9f9675a] {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
}
.uni-popup__mask[data-v-c9f9675a] {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.4);
}
.uni-popup__wrapper[data-v-c9f9675a] {
  position: absolute;
  background-color: #fff;
}
.uni-popup__wrapper-center[data-v-c9f9675a] {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 80%;
  border-radius: 10px;
  display: flex;
  justify-content: center;
}
.uni-popup__wrapper-top[data-v-c9f9675a] {
  top: 0;
  left: 0;
  width: 100%;
}
.uni-popup__wrapper-bottom[data-v-c9f9675a] {
  bottom: 0;
  left: 0;
  width: 100%;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}


.share-popup[data-v-36a41c1e] {
  background-color: #fff;
  border-radius: 16px;
  padding: 24px;
  width: 80%;
  max-width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.share-header[data-v-36a41c1e] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.share-title[data-v-36a41c1e] {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
.close-btn[data-v-36a41c1e] {
  width: 24px;
  height: 24px;
}
.close-btn uni-image[data-v-36a41c1e] {
  width: 100%;
  height: 100%;
}
.share-desc[data-v-36a41c1e] {
  font-size: 14px;
  color: #888;
  text-align: center;
  margin-bottom: 24px;
  display: block;
}
.share-methods[data-v-36a41c1e] {
  display: flex;
  justify-content: space-around;
  padding: 0 10px;
  margin-top: 16px;
}
.share-btn[data-v-36a41c1e] {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: none;
  padding: 8px 12px;
  margin: 0 4px;
}
.share-btn uni-image[data-v-36a41c1e] {
  width: 48px;
  height: 48px;
  margin-bottom: 8px;
}
.share-btn uni-text[data-v-36a41c1e] {
  font-size: 12px;
  color: #666;
}
.view-records-btn[data-v-36a41c1e] {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.625rem;
  margin-top: 0.625rem;
  color: #6b9ac4;
  font-size: 0.875rem;
  border-top: 0.03125rem solid #f0f0f0;
}
.view-records-btn uni-text[data-v-36a41c1e] {
  margin-left: 0.3125rem;
}



/* 全局变量 */
body {
  --warm-gradient: linear-gradient(135deg, #f0f0f0 0%, #e5e5e5 100%);
  --soft-gradient: linear-gradient(135deg, #F8F9FC 0%, #f9f1f3 100%);
  --soft-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
  --menu-hover: rgba(180, 180, 180, 0.1);
  --menu-active: rgba(180, 180, 180, 0.15);
  --card-radius: 18px;
  --item-radius: 14px;
  --transition-speed: 0.3s;
  --primary-color: #888888;
  --secondary-color: #666666;
  --accent-color: #f5f5f5;
  --text-on-primary: #333333;
}

/* 主页面容器 */
.profile-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
  padding-bottom: 32px;
  position: relative;
  overflow: hidden;
}

/* 装饰圆圈 */
.decoration-circle {
  position: absolute;
  border-radius: 50%;
  z-index: 0;
}
.decoration-circle-1 {
  top: -80px;
  right: -80px;
  width: 220px;
  height: 220px;
  background: radial-gradient(circle, rgba(248, 198, 198, 0.15) 0%, rgba(248, 198, 198, 0) 70%);
  animation: float 15s ease-in-out infinite;
}
.decoration-circle-2 {
  bottom: 20%;
  left: -100px;
  width: 280px;
  height: 280px;
  background: radial-gradient(circle, rgba(230, 164, 180, 0.1) 0%, rgba(230, 164, 180, 0) 70%);
  animation: float 18s ease-in-out infinite reverse;
}
.decoration-circle-3 {
  top: 40%;
  right: -60px;
  width: 180px;
  height: 180px;
  background: radial-gradient(circle, rgba(253, 242, 244, 0.3) 0%, rgba(253, 242, 244, 0) 70%);
  animation: float 12s ease-in-out infinite 2s;
}
@keyframes float {
0% {
    transform: translateY(0) translateX(0);
}
50% {
    transform: translateY(-20px) translateX(-10px);
}
100% {
    transform: translateY(0) translateX(0);
}
}

/* 顶部状态栏 */
.status-bar {
  background: var(--warm-gradient);
  width: 100%;
}

/* 用户卡片 */
.user-card {
  background: #ffffff;
  padding: 15px 10px;
  color: #333;
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  border-radius: var(--card-radius);
  overflow: hidden;
  margin: 5px 5px 2px;
}

/* 用户信息区域 */
.user-info {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

/* 头像容器 */
.avatar-container {
  position: relative;
  margin-right: 20px;
}

/* 用户头像 */
.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  border: 3px solid transparent;
  background-clip: padding-box;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-speed);
  position: relative;
  z-index: 2;
  background-color: #fff;
}
.user-avatar:active {
  transform: scale(0.96);
}

/* 普通用户头像边框 */
.avatar-container:not(.is-member) .user-avatar {
  border: 3px solid rgba(240, 240, 240, 0.8);
}

/* 会员用户头像特效 */
.avatar-container.is-member .user-avatar {
  border: 3px solid transparent;
  background-image: linear-gradient(#fff, #fff),
                    linear-gradient(135deg, #FFD700, #FFA500);
  background-origin: border-box;
  background-clip: content-box, border-box;
}

/* 头像光晕效果 */
.avatar-glow {
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border-radius: 50%;
  z-index: 1;
}

/* 普通用户光晕 */
.avatar-container:not(.is-member) .avatar-glow {
  background: rgba(240, 240, 240, 0.8);
  filter: blur(8px);
  opacity: 0.5;
}

/* 会员用户光晕 */
.avatar-container.is-member .avatar-glow {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 165, 0, 0.3));
  filter: blur(10px);
  opacity: 0.8;
  animation: glowPulse 2s infinite;
}
@keyframes glowPulse {
0% {
    opacity: 0.5;
    transform: scale(1);
}
50% {
    opacity: 0.8;
    transform: scale(1.05);
}
100% {
    opacity: 0.5;
    transform: scale(1);
}
}
.vip-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  background: linear-gradient(135deg, #f5a623, #f8c471);
  color: #fff;
  font-size: 11px;
  font-weight: bold;
  padding: 3px 8px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  border: 2px solid #fff;
  z-index: 3;
  transition: all var(--transition-speed);
}
.vip-badge.expired {
  background: linear-gradient(135deg, #b0bec5, #90a4ae);
  opacity: 0.9;
  font-size: 10px;
}
@keyframes pulse {
0% {
    opacity: 0.5;
    transform: scale(1);
}
50% {
    opacity: 0.7;
    transform: scale(1.05);
}
100% {
    opacity: 0.5;
    transform: scale(1);
}
}

/* 用户详情 */
.user-details {
  flex: 1;
  animation: fadeInUp 0.7s ease-out;
}
@keyframes fadeInUp {
from {
    opacity: 0;
    transform: translateY(12px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
.user-name {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 6px;
  color: #333;
  letter-spacing: 0.3px;
  position: relative;
  display: inline-block;
}
.user-id {
  font-size: 13px;
  letter-spacing: 0.3px;
  margin-top: 4px;
  display: inline-block;
  color: #888;
}

/* 会员信息样式 */
.member-info {
  display: flex;
  margin-top: 8px;
  font-size: 13px;
  color: #666;
}
.member-expiry {
  background-color: rgba(245, 166, 35, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  margin-right: 8px;
  color: #f5a623;
  font-weight: 500;
}
.member-days {
  background-color: rgba(76, 175, 80, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  color: #4CAF50;
  font-weight: 500;
}

/* 登录按钮 */
.login-button {
  margin-top: 12px;
  background-color: #f0f0f0;
  color: #555;
  border: none;
  border-radius: 20px;
  padding: 6px 18px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-speed);
  position: relative;
  overflow: hidden;
}
.login-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  transition: left 0.5s;
}
.login-button:active {
  transform: translateY(2px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: #e5e5e5;
}
.login-button:active::before {
  left: 100%;
}
.login-text {
  position: relative;
  z-index: 1;
  letter-spacing: 0.3px;
}



/* 菜单部分 */
.menu-section {
  padding: 2px 5px 10px;
  position: relative;
  z-index: 1;
}
.menu-group {
  background-color: white;
  border-radius: var(--card-radius);
  margin-bottom: 20px;
  margin-top: 5px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  transition: all var(--transition-speed);
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.03);
}
.menu-group:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.05);
}

/* 菜单项 */
.menu-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  transition: all var(--transition-speed);
  position: relative;
}
.menu-item:active {
  background-color: var(--menu-active);
}
.menu-item:last-child {
  border-bottom: none;
}

/* 菜单图标容器 */
.menu-icon-container {
  width: 38px;
  height: 38px;
  border-radius: 10px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  transition: all var(--transition-speed);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}
.menu-item:active .menu-icon-container {
  transform: scale(0.92);
  background-color: #eeeeee;
}
.admin-icon {
  background-color: #f0f0f0;
}

/* 菜单图标 */
.menu-icon {
  width: 20px;
  height: 20px;
  opacity: 0.7;
}

/* 菜单标签 */
.menu-label {
  flex: 1;
  font-size: 15px;
  color: #444;
  font-weight: 500;
  letter-spacing: 0.2px;
}

/* 菜单箭头 */
.menu-arrow {
  width: 16px;
  height: 16px;
  opacity: 0.3;
  transition: transform var(--transition-speed);
  margin-left: 4px;
}
.menu-item:active .menu-arrow {
  transform: translateX(4px);
  opacity: 0.5;
}

/* 徽章 */
.badge {
  background: #f0f0f0;
  color: #666;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 12px;
  margin-right: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-out;
  font-weight: 500;
  letter-spacing: 0.3px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}
@keyframes fadeIn {
from {
    opacity: 0;
    transform: scale(0.8);
}
to {
    opacity: 1;
    transform: scale(1);
}
}

/* 退出登录部分 */
.logout-section {
  padding: 0 15px;
  margin-bottom: 30px;
}
.logout-button {
  width: 100%;
  background-color: white;
  color: #e57373;
  border: none;
  border-radius: var(--card-radius);
  padding: 14px 0;
  font-size: 15px;
  font-weight: 500;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  transition: all var(--transition-speed);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(229, 115, 115, 0.1);
}
.logout-button:active {
  transform: translateY(2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  background-color: #fff5f5;
}
.logout-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(229, 115, 115, 0.05), transparent);
  transition: left 0.5s;
}
.logout-button:active::before {
  left: 100%;
}
.logout-text {
  position: relative;
  z-index: 1;
  letter-spacing: 0.3px;
}

/* 版本信息 */
.version-info {
  text-align: center;
  font-size: 12px;
  color: #aaa;
  padding: 16px;
  letter-spacing: 0.3px;
  position: relative;
  z-index: 1;
  margin-top: 5px;
}
.version-info::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.07), transparent);
}

/* 管理员菜单项特殊样式 */
.admin-menu-item {
  background-color: rgba(0, 0, 0, 0.02);
}

/* 响应式调整 */
@media screen and (min-width: 768px) {
.menu-section {
    padding: 15px 15% 25px;
}
.logout-section {
    padding: 0 15%;
}
}
