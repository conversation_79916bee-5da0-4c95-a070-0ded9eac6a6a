
.uni-popup[data-v-c9f9675a] {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 99;
}
.uni-popup__mask[data-v-c9f9675a] {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.4);
}
.uni-popup__wrapper[data-v-c9f9675a] {
  position: absolute;
  background-color: #fff;
}
.uni-popup__wrapper-center[data-v-c9f9675a] {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 80%;
  border-radius: 10px;
  display: flex;
  justify-content: center;
}
.uni-popup__wrapper-top[data-v-c9f9675a] {
  top: 0;
  left: 0;
  width: 100%;
}
.uni-popup__wrapper-bottom[data-v-c9f9675a] {
  bottom: 0;
  left: 0;
  width: 100%;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}


.uni-popup[data-v-4008e257] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}
.uni-popup__mask[data-v-4008e257] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
}
.uni-popup__wrapper[data-v-4008e257] {
  position: absolute;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}
.uni-popup__wrapper-center[data-v-4008e257] {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 80%;
  max-height: 80%;
}
.uni-popup__wrapper-top[data-v-4008e257] {
  top: 0;
  left: 0;
  right: 0;
  border-radius: 0 0 8px 8px;
}
.uni-popup__wrapper-bottom[data-v-4008e257] {
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 8px 8px 0 0;
}


.register-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 40px 30px;
  background-color: var(--bg-primary);
}
.register-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40px;
}
.register-logo {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
}
.register-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 8px;
}
.register-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
}
.register-form {
  display: flex;
  flex-direction: column;
}
.form-group {
  margin-bottom: 20px;
}
.form-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
  display: block;
}
.phone-input-container {
  display: flex;
  border: 1px solid var(--border-regular);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}
.country-code {
  display: flex;
  align-items: center;
  padding: 0 12px;
  background-color: var(--bg-tertiary);
  border-right: 1px solid var(--border-regular);
}
.phone-input {
  flex: 1;
  height: 48px;
  padding: 0 12px;
  font-size: 16px;
  border: none;
}
.verification-code-container {
  display: flex;
  border: 1px solid var(--border-regular);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}
.verification-code-input {
  flex: 1;
  height: 48px;
  padding: 0 12px;
  font-size: 16px;
  border: none;
}
.send-code-button {
  width: 120px;
  background-color: var(--primary-color);
  color: white;
  font-size: 14px;
  border: none;
  border-radius: 0;
  height: 48px; /* 确保与输入框高度一致 */
  display: flex;
  justify-content: center;
  align-items: center; /* 垂直居中文本 */
}
.send-code-button[disabled] {
  background-color: var(--border-regular);
  color: var(--text-disabled);
}
.nickname-input {
  width: 100%;
  height: 48px;
  padding: 0 12px;
  font-size: 16px;
  border: 1px solid var(--border-regular);
  border-radius: var(--border-radius-md);
}
.agreement-container {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.agreement-text {
  font-size: 14px;
  color: var(--text-secondary);
  margin-left: 8px;
}
.agreement-link {
  color: var(--primary-color);
}
.register-button {
  height: 48px;
  background-color: var(--primary-color);
  color: white;
  font-size: 16px;
  border: none;
  border-radius: var(--border-radius-md);
  margin-bottom: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.register-button[disabled] {
  background-color: var(--border-regular);
  color: var(--text-disabled);
}
.login-link-container {
  display: flex;
  justify-content: center;
  font-size: 14px;
  color: var(--text-secondary);
}
.login-link {
  color: var(--primary-color);
  margin-left: 4px;
}
.country-code-picker {
  background-color: var(--bg-primary);
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  overflow: hidden;
}
.country-code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-light);
}
.country-code-title {
  font-size: 16px;
  font-weight: 500;
}
.close-button {
  background: none;
  border: none;
  font-size: 20px;
  color: var(--text-secondary);
  padding: 4px;
}
.country-code-list {
  max-height: 300px;
}
.country-code-item {
  display: flex;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--border-light);
}
.country-name {
  font-size: 16px;
  color: var(--text-primary);
}
.country-code-value {
  font-size: 16px;
  color: var(--text-secondary);
}

/* 图标字体 */
@font-face {
  font-family: 'iconfont';
  src: url('../../static/fonts/iconfont.ttf') format('truetype');
}
.iconfont {
  font-family: 'iconfont';
}
.icon-down:before {
  content: '\e6b9';
}
.icon-close:before {
  content: '\e6a7';
}
.icon-loading {
  display: inline-block;
  animation: rotate 1s linear infinite;
}
.icon-loading:before {
  content: '\e6a2';
}
@keyframes rotate {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
