import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { List, Card, Button, Spin, Empty, message, Typography, Tag, Space, Modal, Form, Input, DatePicker, InputNumber, Popconfirm } from 'antd';
import { PlusOutlined, FileTextOutlined, Bar<PERSON><PERSON>Outlined, DeleteOutlined } from '@ant-design/icons';
import counselingService from '../../services/counselingService';
import moment from 'moment';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const SessionList = () => {
  const navigate = useNavigate();
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [creating, setCreating] = useState(false);

  // 加载会话列表
  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    try {
      setLoading(true);
      const result = await counselingService.getSessions();

      if (result.success) {
        setSessions(result.data);
      } else {
        message.error('获取会话列表失败');
      }
    } catch (error) {
      console.error('获取会话列表失败:', error);
      message.error('获取会话列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除会话
  const handleDeleteSession = async (sessionId) => {
    try {
      const result = await counselingService.deleteSession(sessionId);

      if (result.success) {
        message.success('会话删除成功');
        // 刷新会话列表
        await loadSessions();
      } else {
        message.error(result.error || '删除会话失败');
      }
    } catch (error) {
      console.error('删除会话失败:', error);
      message.error('删除会话失败');
    }
  };

  // 创建新会话
  const createSession = async (values) => {
    try {
      setCreating(true);

      // 格式化日期
      const formattedValues = {
        ...values,
        session_date: values.session_date ? values.session_date.format('YYYY-MM-DD HH:mm:ss') : undefined
      };

      const result = await counselingService.createSession(formattedValues);

      if (result.id) {
        message.success('创建会话成功');
        setModalVisible(false);
        form.resetFields();

        // 刷新会话列表
        await loadSessions();

        // 导航到新会话详情页
        navigate(`/counseling/sessions/${result.id}`);
      } else {
        message.error('创建会话失败');
      }
    } catch (error) {
      console.error('创建会话失败:', error);
      message.error('创建会话失败');
    } finally {
      setCreating(false);
    }
  };

  // 渲染会话状态标签
  const renderStatusTag = (session) => {
    if (session.analysis) {
      const status = session.analysis.status;

      if (status === 'completed') {
        return <Tag color="green">分析完成</Tag>;
      } else if (status === 'processing') {
        return <Tag color="blue">分析中</Tag>;
      } else if (status === 'failed') {
        return <Tag color="red">分析失败</Tag>;
      } else {
        return <Tag color="orange">待分析</Tag>;
      }
    }

    return session.dialogue ? <Tag color="cyan">已录入对话</Tag> : <Tag>未录入对话</Tag>;
  };

  return (
    <div className="session-list">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <Title level={3}>心理咨询会话</Title>

        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setModalVisible(true)}
        >
          创建新会话
        </Button>
      </div>

      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '20px' }}>加载会话列表...</div>
        </div>
      ) : sessions.length === 0 ? (
        <Empty
          description="暂无会话记录"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button
            type="primary"
            onClick={() => setModalVisible(true)}
          >
            创建新会话
          </Button>
        </Empty>
      ) : (
        <List
          grid={{
            gutter: 16,
            xs: 1,
            sm: 1,
            md: 2,
            lg: 3,
            xl: 3,
            xxl: 4
          }}
          dataSource={sessions}
          renderItem={session => (
            <List.Item>
              <Card
                hoverable
                title={
                  <Link to={`/counseling/sessions/${session.id}`}>
                    {session.title || '咨询会话'}
                  </Link>
                }
                extra={renderStatusTag(session)}
              >
                <div style={{ marginBottom: '12px' }}>
                  <Text type="secondary">
                    {moment(session.session_date).format('YYYY-MM-DD HH:mm')}
                    {(() => {
                      // 检查是否有录音记录
                      const hasRecordings = session.recordings && Array.isArray(session.recordings) && session.recordings.length > 0;

                      // 如果有录音记录
                      if (hasRecordings) {
                        const recording = session.recordings[0];

                        // 如果录音有时长信息，显示实际录音时长
                        if (recording.duration_seconds && recording.duration_seconds > 0) {
                          const totalSeconds = recording.duration_seconds;
                          const minutes = Math.floor(totalSeconds / 60);
                          const seconds = Math.floor(totalSeconds % 60);
                          return ` | ${minutes}:${seconds.toString().padStart(2, '0')}`;
                        }

                        // 如果有录音但没有时长信息，检查是否有分析报告
                        if (session.analysis && session.analysis.status === 'completed') {
                          // 有分析报告但没有时长，显示配置时长或"已分析"
                          return session.duration_minutes ? ` | ${session.duration_minutes} 分钟` : ' | 已分析';
                        }

                        // 有录音但还没有分析完成，显示配置时长或"录音中"
                        return session.duration_minutes ? ` | ${session.duration_minutes} 分钟` : ' | 录音中';
                      }

                      // 没有录音记录，显示配置时长或未设置
                      return session.duration_minutes ? ` | ${session.duration_minutes} 分钟` : '';
                    })()}
                  </Text>
                </div>

                <Paragraph ellipsis={{ rows: 2 }}>
                  {session.description || '无描述'}
                </Paragraph>

                <div style={{ marginTop: '16px' }}>
                  <Space>
                    <Link to={`/counseling/sessions/${session.id}`}>
                      <Button
                        type="text"
                        icon={<FileTextOutlined />}
                        size="small"
                      >
                        查看详情
                      </Button>
                    </Link>

                    {session.analysis && session.analysis.status === 'completed' && (
                      <Link to={`/counseling/sessions/${session.id}`} state={{ activeTab: 'analysis' }}>
                        <Button
                          type="text"
                          icon={<BarChartOutlined />}
                          size="small"
                        >
                          查看分析
                        </Button>
                      </Link>
                    )}

                    <Popconfirm
                      title="删除会话"
                      description="确定要删除这个会话吗？所有相关的对话和分析数据都将被删除。"
                      onConfirm={() => handleDeleteSession(session.id)}
                      okText="删除"
                      cancelText="取消"
                      okButtonProps={{ danger: true }}
                    >
                      <Button
                        type="text"
                        icon={<DeleteOutlined />}
                        size="small"
                        danger
                      >
                        删除
                      </Button>
                    </Popconfirm>
                  </Space>
                </div>
              </Card>
            </List.Item>
          )}
        />
      )}

      {/* 创建会话表单 */}
      <Modal
        title="创建新会话"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={createSession}
        >
          <Form.Item
            name="title"
            label="会话标题"
            rules={[{ required: true, message: '请输入会话标题' }]}
          >
            <Input placeholder="请输入会话标题" />
          </Form.Item>

          <Form.Item
            name="description"
            label="会话描述"
          >
            <TextArea
              placeholder="请输入会话描述"
              rows={4}
            />
          </Form.Item>

          <Form.Item
            name="session_date"
            label="会话日期"
            initialValue={moment()}
          >
            <DatePicker
              showTime
              format="YYYY-MM-DD HH:mm"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="duration_minutes"
            label="会话时长（分钟）"
          >
            <InputNumber
              min={1}
              max={240}
              style={{ width: '100%' }}
              placeholder="请输入会话时长"
            />
          </Form.Item>

          <Form.Item>
            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={creating}
              >
                创建
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SessionList;
