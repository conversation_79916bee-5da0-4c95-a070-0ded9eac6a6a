# 心理咨询时长显示修复测试

## 修复内容

### 问题描述
心理咨询页面的时长显示存在以下问题：
1. 如果没有设置时长且没有报告生成，应显示"未设置"
2. 如果设置了时长但没有开始咨询录制，应显示用户配置的时长
3. 如果有进行对话分析且有报告生成，应显示实际的录音时长

### 修复方案

#### 1. 修改录音上传逻辑
- 在 `app/services/counselingService.js` 中修改 `uploadRecording` 方法，支持传递录音时长参数
- 在 `app/pages/counseling/recording.vue` 中修改上传录音时传递实际录音时长

#### 2. 优化时长显示逻辑
在以下文件中统一时长显示逻辑：
- `app/pages/counseling/sessions.vue` - 会话列表页面
- `app/pages/counseling/detail.vue` - 会话详情页面
- `frontend/src/components/counseling/CounselingSessionList.jsx` - Web端会话列表
- `frontend/src/pages/counseling/DesktopSessionTable.jsx` - Web端桌面表格
- `frontend/src/pages/counseling/SessionList.jsx` - Web端会话列表

#### 3. 时长显示规则（简洁版，去掉括号内容）
```javascript
// 检查是否有录音记录
const hasRecordings = session.recordings && Array.isArray(session.recordings) && session.recordings.length > 0;

// 如果有录音记录
if (hasRecordings) {
  const recording = session.recordings[0];

  // 如果录音有时长信息，显示实际录音时长
  if (recording.duration_seconds && recording.duration_seconds > 0) {
    const totalSeconds = recording.duration_seconds;
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = Math.floor(totalSeconds % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  // 如果有录音但没有时长信息，检查是否有分析报告
  if (session.analysis && session.analysis.status === 'completed') {
    // 有分析报告但没有时长，显示配置时长或"已分析"
    return session.duration_minutes ? `${session.duration_minutes} 分钟` : '已分析';
  }

  // 有录音但还没有分析完成，显示配置时长或"录音中"
  return session.duration_minutes ? `${session.duration_minutes} 分钟` : '录音中';
}

// 没有录音记录，显示配置时长或未设置
return session.duration_minutes ? `${session.duration_minutes} 分钟` : '未设置';
```

## 测试场景

### 场景1：未设置时长且没有录音
- 预期显示：`未设置`
- 测试步骤：创建新会话，不设置时长，不进行录音

### 场景2：设置了时长但没有录音
- 预期显示：`30 分钟`（假设设置了30分钟）
- 测试步骤：创建新会话，设置时长为30分钟，不进行录音

### 场景3：有录音但没有时长信息，且没有分析
- 预期显示：`30 分钟`（假设设置了30分钟）或 `录音中`（如果没有设置时长）
- 测试步骤：创建会话，设置时长，进行录音但不传递时长参数

### 场景4：有录音但没有时长信息，且有分析报告
- 预期显示：`30 分钟`（假设设置了30分钟）或 `已分析`（如果没有设置时长）
- 测试步骤：创建会话，设置时长，进行录音，完成分析，但录音没有时长信息

### 场景5：有录音且有时长信息
- 预期显示：`25:30`（假设实际录音25分30秒）
- 测试步骤：创建会话，进行录音，传递实际录音时长

## 修改的文件列表

1. `app/services/counselingService.js` - 修改uploadRecording方法支持时长参数
2. `app/pages/counseling/recording.vue` - 修改上传录音时传递时长
3. `app/pages/counseling/sessions.vue` - 优化时长显示逻辑
4. `app/pages/counseling/detail.vue` - 优化时长显示逻辑
5. `frontend/src/components/counseling/CounselingSessionList.jsx` - 优化时长显示逻辑
6. `frontend/src/pages/counseling/DesktopSessionTable.jsx` - 优化时长显示逻辑
7. `frontend/src/pages/counseling/SessionList.jsx` - 优化时长显示逻辑

## 验证方法

1. 启动应用
2. 创建新的心理咨询会话
3. 测试各种场景下的时长显示
4. 确认移动端和Web端显示一致
5. 确认录音时长能正确传递到后端并保存
